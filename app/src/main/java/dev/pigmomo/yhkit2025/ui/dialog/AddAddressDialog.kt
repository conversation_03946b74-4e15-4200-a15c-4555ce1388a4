package dev.pigmomo.yhkit2025.ui.dialog

import android.widget.Toast
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.List
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.api.model.user.ShopInfo
import dev.pigmomo.yhkit2025.api.utils.AddressUtils
import dev.pigmomo.yhkit2025.api.utils.AddressUtils.replaceUUID
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.common.ClipboardUtils
import dev.pigmomo.yhkit2025.utils.common.EncryptionUtils
import org.json.JSONObject

/**
 * 添加地址对话框
 * 用于展示和处理添加地址的界面
 *
 * @param addAddressStatus 是否显示对话框
 * @param currentAccount 当前账号
 * @param phone 电话号码
 * @param addAddressStr 地址参数字符串
 * @param addAddressLocationStr 地址定位参数字符串
 * @param randomDetailsRegex 随机详细地址正则表达式
 * @param fastenAddress 是否固定地址
 * @param fastenLatLng 是否固定经纬度
 * @param orderConfirmComment 订单备注
 * @param addAddressShopItem 添加地址的店铺
 * @param onFastenAddressChange 固定地址变更回调
 * @param onFastenLatLngChange 固定经纬度变更回调
 * @param onAddressSubmit 地址提交回调
 * @param onAddressConfigSave 地址配置保存回调
 * @param onDismiss 对话框关闭回调
 * @param getPreConfigAddressList 获取预配置地址列表回调
 */
@Composable
fun AddAddressDialog(
    addAddressStatus: Boolean,
    currentAccount: OrderTokenEntity,
    phone: String,
    addAddressStr: String,
    addAddressLocationStr: String,
    randomDetailsRegex: String,
    fastenAddress: Boolean,
    fastenLatLng: Boolean,
    orderConfirmComment: String,
    addAddressShopItem: ShopInfo?,
    onFastenAddressChange: (Boolean) -> Unit,
    onFastenLatLngChange: (Boolean) -> Unit,
    onAddressSubmit: (String, String, String, String) -> Unit,
    onAddressConfigSave: (String, String, String, String, ShopInfo?) -> Unit,
    onDismiss: () -> Unit,
    getPreConfigAddressList: () -> List<JSONObject>,
    onCopyAddressConfig: (String) -> Unit,
    onPasteAddressConfig: () -> Unit
) {
    if (!addAddressStatus) return

    // 检查先决条件
    val context = LocalContext.current

    // 界面状态
    var nameValue by remember(phone) {
        mutableStateOf(phone.takeIf { it.length >= 5 }?.substring(0, 5) ?: phone)
    }
    var phoneValue by remember(phone) { mutableStateOf(phone) }
    var addressStrValue by remember(addAddressStr) { mutableStateOf(addAddressStr) }
    var locationStrValue by remember(addAddressLocationStr) { mutableStateOf(addAddressLocationStr) }
    var detailsRegexValue by remember(randomDetailsRegex) { mutableStateOf(randomDetailsRegex) }
    var preConfigListStatus by remember { mutableStateOf(false) }

    // 显示地址预览
    var showDetailAddressStr by remember { mutableStateOf("") }

    LaunchedEffect(addressStrValue, nameValue, phoneValue) {
        if (addressStrValue.isNotEmpty()) {
            try {
                val detailAddr = JSONObject(addressStrValue)
                showDetailAddressStr =
                    "$nameValue,$phoneValue,${detailAddr.getString("area")}${detailAddr.getString("detail")}"
            } catch (e: Exception) {
                showDetailAddressStr = "请输入正确的地址参数格式"
            }
        }
    }

    // 检查输入有效性
    val isInputValid by remember(detailsRegexValue, addressStrValue, locationStrValue) {
        derivedStateOf {
            detailsRegexValue.isNotEmpty() &&
                    addressStrValue.isNotEmpty() &&
                    locationStrValue.isNotEmpty()
        }
    }

    // 预置地址列表
    val preConfigList = remember { getPreConfigAddressList() }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = dialogContainerColor(),
        title = { Text("添加地址") },
        text = {
            Column {
                // 地址操作按钮卡片
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {
                        // 随机化按钮
                        TokenActionButton(
                            imageVector = Icons.Filled.Refresh,
                            text = "随机",
                            onClick = {
                                if (addressStrValue.isEmpty()) {
                                    Toast.makeText(context, "请先输入地址参数", Toast.LENGTH_SHORT)
                                        .show()
                                    return@TokenActionButton
                                }

                                // 使用AddressUtils生成随机姓名
                                nameValue = AddressUtils.randomName()

                                // 使用AddressUtils替换详细地址
                                addressStrValue = AddressUtils.replaceDetail(
                                    addressStrValue,
                                    detailsRegexValue.replace(" ", "")
                                )

                                // 替换UUID
                                addressStrValue = AddressUtils.replaceUUID(addressStrValue)
                                locationStrValue = AddressUtils.replaceUUID(locationStrValue)

                                // 如果不固定经纬度，则随机化经纬度
                                if (!fastenLatLng) {
                                    try {
                                        val locationJSON = JSONObject(locationStrValue)
                                        val lat = locationJSON.getString("lat")
                                        val lng = locationJSON.getString("lng")
                                        locationStrValue = locationStrValue.replace(
                                            lat,
                                            AddressUtils.adjustLastThreeDigits(lat)
                                        )
                                        locationStrValue = locationStrValue.replace(
                                            lng,
                                            AddressUtils.adjustLastThreeDigits(lng)
                                        )
                                    } catch (e: Exception) {
                                        Toast.makeText(
                                            context,
                                            "经纬度参数格式错误",
                                            Toast.LENGTH_SHORT
                                        ).show()
                                    }
                                }

                                Toast.makeText(
                                    context,
                                    "已随机化地址信息",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        )

                        // 保存配置按钮
                        TokenActionButton(
                            icon = R.drawable.baseline_save_24,
                            text = "地址配置",
                            onClick = {
                                if (addressStrValue.isEmpty() || locationStrValue.isEmpty()) {
                                    Toast.makeText(context, "地址参数不能为空", Toast.LENGTH_SHORT)
                                        .show()
                                    return@TokenActionButton
                                }

                                val cleanDetailsRegex = detailsRegexValue.replace(" ", "")
                                onAddressConfigSave(
                                    addressStrValue,
                                    locationStrValue,
                                    cleanDetailsRegex,
                                    orderConfirmComment,
                                    addAddressShopItem
                                )
                                Toast.makeText(
                                    context,
                                    "地址配置保存成功",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        )

                        // 复制按钮
                        TokenActionButton(
                            icon = R.drawable.baseline_copy_all_24,
                            text = "复制配置",
                            onClick = {
                                val inputStr =
                                    if (addressStrValue.isNotEmpty() && locationStrValue.isNotEmpty() && detailsRegexValue.isNotEmpty())
                                        "$addressStrValue;$locationStrValue;$detailsRegexValue;"
                                    else
                                        ""
                                onCopyAddressConfig(inputStr)
                            }
                        )

                        // 粘贴按钮
                        TokenActionButton(
                            icon = R.drawable.baseline_content_paste_24,
                            text = "粘贴配置",
                            onClick = {
                                onPasteAddressConfig()
                            }
                        )

                        // 固定地址按钮
                        TokenActionButton(
                            imageVector = Icons.Filled.Lock,
                            text = "地址固定",
                            onClick = {
                                onFastenAddressChange(!fastenAddress)
                            },
                            tint = if (!fastenAddress)
                                LocalContentColor.current.copy(alpha = 0.5f)
                            else
                                LocalContentColor.current,
                        )

                        // 固定经纬度按钮
                        TokenActionButton(
                            imageVector = Icons.Filled.LocationOn,
                            text = "经纬度固定",
                            onClick = { onFastenLatLngChange(!fastenLatLng) },
                            tint = if (!fastenLatLng)
                                LocalContentColor.current.copy(alpha = 0.5f)
                            else
                                LocalContentColor.current,
                        )

                        // 预置地址列表按钮
                        TokenActionButton(
                            imageVector = Icons.AutoMirrored.Filled.List,
                            text = "预置",
                            onClick = { preConfigListStatus = true }
                        )

                        // 预置地址下拉菜单
                        DropdownMenu(
                            expanded = preConfigListStatus,
                            onDismissRequest = { preConfigListStatus = false }
                        ) {
                            if (preConfigList.isNotEmpty()) {
                                preConfigList.forEach { preConfig ->
                                    val preName = preConfig.getString("preName")
                                    DropdownMenuItem(
                                        text = {
                                            Row(verticalAlignment = Alignment.CenterVertically) {
                                                Text(
                                                    preName,
                                                    modifier = Modifier
                                                        .weight(1f)
                                                        .horizontalScroll(rememberScrollState())
                                                )
                                            }
                                        },
                                        onClick = {
                                            try {
                                                detailsRegexValue =
                                                    preConfig.getString("randomDetailsRegexLs")
                                                addressStrValue =
                                                    preConfig.getString("addAddressStr")
                                                locationStrValue =
                                                    preConfig.getString("addAddressLocationStr")
                                                val comment = if (preConfig.has("comment"))
                                                    preConfig.getString("comment")
                                                else
                                                    ""

                                                // 更新UI状态，以便预览能够反映新的地址
                                                try {
                                                    val detailAddr = JSONObject(addressStrValue)
                                                    showDetailAddressStr =
                                                        "$nameValue,$phoneValue,${
                                                            detailAddr.getString("area")
                                                        }${detailAddr.getString("detail")}"
                                                } catch (e: Exception) {
                                                    showDetailAddressStr =
                                                        "请输入正确的地址参数格式"
                                                }

                                                onAddressConfigSave(
                                                    addressStrValue,
                                                    locationStrValue,
                                                    detailsRegexValue,
                                                    comment,
                                                    addAddressShopItem
                                                )
                                                Toast.makeText(
                                                    context,
                                                    "已加载预置地址：$preName",
                                                    Toast.LENGTH_SHORT
                                                ).show()
                                            } catch (e: Exception) {
                                                Toast.makeText(
                                                    context,
                                                    "加载预置地址失败",
                                                    Toast.LENGTH_SHORT
                                                ).show()
                                            }
                                            preConfigListStatus = false
                                        }
                                    )
                                }
                            }
                        }
                    }
                }

                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = cardThemeOverlay()
                ) {
                    if (addressStrValue.isNotEmpty()) {
                        Text(
                            showDetailAddressStr,
                            modifier = Modifier
                                .padding(8.dp)
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState()),
                            fontSize = 12.sp
                        )
                    } else {
                        Text(
                            "地址参数为空，请输入地址参数",
                            modifier = Modifier.padding(8.dp),
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                }

                // 姓名电话输入框
                OutlinedTextField(
                    value = "$nameValue,$phoneValue",
                    onValueChange = {
                        val parts = it.split(",")
                        if (parts.isNotEmpty()) nameValue = parts[0]
                        if (parts.size > 1) phoneValue = parts[1]
                    },
                    label = { Text("姓名电话") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 详细地址输入框
                OutlinedTextField(
                    value = detailsRegexValue,
                    onValueChange = { detailsRegexValue = it },
                    label = { Text("XX单元XX栋XX-X号") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 地址参数输入框
                OutlinedTextField(
                    value = addressStrValue,
                    onValueChange = { addressStrValue = it },
                    label = { Text("地址参数") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 定位参数输入框
                OutlinedTextField(
                    value = locationStrValue,
                    onValueChange = { locationStrValue = it },
                    label = { Text("定位参数") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    // 检查输入有效性
                    if (!isInputValid) {
                        Toast.makeText(context, "配置解析错误，请检查配置", Toast.LENGTH_SHORT)
                            .show()
                        return@Button
                    }

                    // 提交地址
                    onAddressSubmit(
                        addressStrValue,
                        locationStrValue,
                        nameValue,
                        phoneValue,
                    )

                    // 关闭对话框
                    onDismiss()
                },
                enabled = isInputValid && !fastenAddress
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            Button(
                onClick = onDismiss,
            ) {
                Text("取消")
            }
        }
    )
}