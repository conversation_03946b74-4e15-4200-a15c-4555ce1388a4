package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import dev.pigmomo.yhkit2025.api.model.order.AfterSalesOrder
import dev.pigmomo.yhkit2025.api.model.order.AfterSalesOrderItem
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 售后订单列表对话框
 *
 * @param onDismiss 关闭对话框回调
 * @param orderList 售后订单列表
 * @param page 当前页码
 * @param pageCount 总页数
 * @param onLoadMore 加载更多数据回调
 * @param onSetAfterSalesOrderId 设置售后订单ID
 */
@Composable
fun AfterSalesListDialog(
    onDismiss: () -> Unit,
    orderList: List<AfterSalesOrder>,
    page: Int,
    pageCount: Int,
    onLoadMore: (Int) -> Unit,
    onSetAfterSalesOrderId: (String) -> Unit
) {
    val listState = rememberLazyListState()

    // 检测是否滚动到底部，如果是则加载更多
    val shouldLoadMore = remember {
        derivedStateOf {
            val layoutInfo = listState.layoutInfo
            val totalItemsNumber = layoutInfo.totalItemsCount
            val lastVisibleItemIndex = (layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0) + 1

            lastVisibleItemIndex > 0 && lastVisibleItemIndex >= totalItemsNumber - 2 && page < pageCount - 1
        }
    }

    // 当滚动到底部时加载更多
    LaunchedEffect(shouldLoadMore.value) {
        if (shouldLoadMore.value) {
            val nextPage = page + 1
            if (nextPage < pageCount) {
                onLoadMore(nextPage)
            }
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Column {
                Text("售后订单列表")
            }
        },
        text = {
            Column(
                modifier = Modifier
                    .heightIn(max = 300.dp)
            ) {
                LazyColumn(
                    state = listState,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    items(orderList) { order ->
                        AfterSalesOrderItem(
                            order = order,
                            onSetAfterSalesOrderId = onSetAfterSalesOrderId
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                    }

                    item {
                        if (!shouldLoadMore.value && orderList.size > 3) {
                            Text(
                                text = "到底啦！！！",
                                fontSize = 10.sp,
                                lineHeight = 12.sp,
                                color = Color.Gray,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {},
        containerColor = dialogContainerColor()
    )
}

/**
 * 售后订单项
 *
 * @param order 售后订单
 * @param onSetAfterSalesOrderId 设置售后订单ID
 */
@Composable
fun AfterSalesOrderItem(
    order: AfterSalesOrder,
    onSetAfterSalesOrderId: (String) -> Unit
) {
    val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    val formattedDate = dateFormat.format(Date(order.applytime))

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = cardThemeOverlay(),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .clickable {
                    onSetAfterSalesOrderId(order.id)
                },
        ) {
            // 订单头部信息
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 12.dp, end = 12.dp, top = 12.dp)
            ) {
                Text(
                    text = order.typedesc + " 单号: ${order.id}",
                    fontWeight = FontWeight.Bold,
                    fontSize = 14.sp,
                    modifier = Modifier.weight(1f).horizontalScroll(rememberScrollState()),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Text(
                    text = order.statusdesc,
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }

            Text(
                text = "申请时间: $formattedDate",
                fontSize = 12.sp,
                color = Color.Gray,
                modifier = Modifier.padding(start = 12.dp)
            )

            // 商品列表
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .padding(start = 12.dp, end = 12.dp, bottom = 12.dp)
                    .fillMaxWidth()
            ) {
                // 商品图片列表（可横向滚动）
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .weight(1f)
                        .horizontalScroll(rememberScrollState())
                ) {
                    order.list.forEach { item ->
                        AfterSalesItemView(item)
                    }
                }

                // 总数量显示
                if (order.count > 0) {
                    Text(
                        text = "共 ${order.count.toInt()} 件",
                        fontSize = 12.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }
    }
}

/**
 * 售后订单商品行
 *
 * @param item 售后订单商品项
 */
@Composable
fun AfterSalesItemView(item: AfterSalesOrderItem) {
    Box(modifier = Modifier.padding(end = 4.dp)) {
        // 商品图片
        AsyncImage(
            model = item.imageurl,
            contentDescription = item.title,
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(4.dp)),
            contentScale = ContentScale.Crop
        )
    }
}