package dev.pigmomo.yhkit2025.ui.dialog

import android.widget.Toast
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.FailedTokenIndexRecordUtils
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 抢券配置数据类
 */
data class CouponGrabbingData(
    // 请求体参数
    val requestBody: String = """{"promotioncode":"","securityversion":"tc_v3","captchaticket":"","captcharandstr":"","assemblyid":"","resourceid":"","couponamount":-100,"couponcatalog":6,"coupondesc":"","orderminamount":800}""",
    // 定时配置
    val targetTime: String = "",
    // 多线程配置
    val multiThreadRangeStr: String = "",
    // 是否启用
    val isEnabled: Boolean = false
)

/**
 * 抢券配置对话框
 *
 * @param onDismiss 关闭对话框的回调
 * @param couponGrabbingConfig 当前抢券配置
 * @param onShowMultiThreadDialog 显示多线程配置对话框的回调
 * @param multiThreadRangeList 多线程配置数组
 * @param multiThreadEnabled 是否启用多线程
 * @param setMultiThreadEnabled 设置多线程启用状态的回调
 * @param onSaveConfig 保存配置的回调
 * @param onStartGrabbing 开始抢券的回调
 */
@Composable
fun CouponGrabbingConfigDialog(
    onDismiss: () -> Unit,
    couponGrabbingConfig: String,
    onShowMultiThreadDialog: () -> Unit = {},
    multiThreadRangeList: List<Int> = emptyList(),
    multiThreadEnabled: Boolean = false,
    setMultiThreadEnabled: (Boolean) -> Unit = {},
    onSaveConfig: (String) -> Unit = {},
    onStartGrabbing: (String, Boolean, String, List<Int>) -> Unit = { _, _, _, _ -> },
    fastenAddress: Boolean,
    onFastenAddressChange: (Boolean) -> Unit,
) {
    val context = LocalContext.current

    // 解析配置
    val initialConfig = try {
        val json = JSONObject(couponGrabbingConfig)
        CouponGrabbingData(
            requestBody = json.optString(
                "requestBody",
                """{"promotioncode":"","securityversion":"tc_v3","captchaticket":"","captcharandstr":"","assemblyid":"","resourceid":"","couponamount":-100,"couponcatalog":6,"coupondesc":"","orderminamount":800}"""
            ),
            targetTime = json.optString("targetTime", ""),
            multiThreadRangeStr = json.optString("multiThreadRangeStr", ""),
            isEnabled = json.optBoolean("isEnabled", false)
        )
    } catch (e: Exception) {
        CouponGrabbingData()
    }

    // 抢券配置状态
    var requestBody by remember(initialConfig.requestBody) {
        mutableStateOf(initialConfig.requestBody)
    }

    // 定时配置状态
    var timerEnabled by remember { mutableStateOf(false) }
    var targetTimeStr by remember {
        mutableStateOf(
            initialConfig.targetTime.ifEmpty {
                SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                    .format(Date(System.currentTimeMillis() + 60000)) // 默认为当前时间+1分钟
            }
        )
    }

    // 验证JSON格式
    fun validateJsonFormat(): Boolean {
        return try {
            val json = JSONObject(requestBody)
            json.optString("promotioncode", "") != ""
        } catch (e: Exception) {
            false
        }
    }

    // 验证定时配置
    fun validateTimerConfig(): Boolean {
        if (!timerEnabled) return true

        return try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            val targetTime = dateFormat.parse(targetTimeStr)?.time ?: return false
            val currentTime = System.currentTimeMillis()

            // 目标时间必须在当前时间之后
            targetTime > currentTime
        } catch (e: Exception) {
            false
        }
    }

    // 验证所有配置
    val isConfigValid = validateJsonFormat() && validateTimerConfig() &&
            (!timerEnabled || multiThreadEnabled)

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("抢券配置") },
        containerColor = dialogContainerColor(),
        text = {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 操作按钮卡片
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {
                        // 多线程配置按钮
                        TokenActionButton(
                            imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                            text = "多线程",
                            onClick = {
                                if (!multiThreadEnabled) {
                                    onShowMultiThreadDialog()
                                } else {
                                    setMultiThreadEnabled(false)
                                }
                            },
                            tint = if (!multiThreadEnabled) Color(0xFF48454E).copy(alpha = 0.5f) else Color(
                                0xFF48454E
                            )
                        )

                        // 定时配置按钮
                        TokenActionButton(
                            imageVector = Icons.Filled.DateRange,
                            text = "定时",
                            onClick = {
                                if (timerEnabled) {
                                    timerEnabled = false
                                } else {
                                    if (validateTimerConfig()) {
                                        timerEnabled = true
                                    } else {
                                        Toast.makeText(
                                            context,
                                            "时间格式错误或时间已超过",
                                            Toast.LENGTH_SHORT
                                        ).show()
                                    }
                                }
                            },
                            tint = if (!timerEnabled) Color(0xFF48454E).copy(alpha = 0.5f) else Color(
                                0xFF48454E
                            )
                        )

                        TokenActionButton(
                            imageVector = Icons.Filled.Lock,
                            text = "地址固定",
                            onClick = { onFastenAddressChange(!fastenAddress) },
                            tint = if (!fastenAddress)
                                Color(0xFF48454E).copy(alpha = 0.5f)
                            else
                                Color(0xFF48454E),
                        )

                        // 保存配置按钮
                        TokenActionButton(
                            icon = R.drawable.baseline_save_24,
                            text = "保存配置",
                            onClick = {
                                if (validateJsonFormat()) {
                                    try {
                                        // 构建配置JSON
                                        val configJson = JSONObject().apply {
                                            put("requestBody", requestBody)
                                            put("targetTime", targetTimeStr)
                                            put(
                                                "multiThreadRangeStr",
                                                multiThreadRangeList.joinToString(",")
                                            )
                                            put("isEnabled", false)
                                        }

                                        onSaveConfig(configJson.toString())
                                        Toast.makeText(context, "配置已保存", Toast.LENGTH_SHORT)
                                            .show()
                                    } catch (e: Exception) {
                                        Toast.makeText(
                                            context,
                                            "保存配置错误: ${e.message}",
                                            Toast.LENGTH_SHORT
                                        ).show()
                                    }
                                } else {
                                    Toast.makeText(
                                        context,
                                        "配置格式错误，请检查",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                            }
                        )
                    }
                }

                Column(
                    modifier = Modifier.padding(vertical = 4.dp)
                ) {
                    OutlinedTextField(
                        value = targetTimeStr,
                        onValueChange = { targetTimeStr = it },
                        label = { Text(if (validateTimerConfig()) "目标时间" else "时间格式错误或时间已超过") },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        isError = !validateTimerConfig(),
                        trailingIcon = {
                            IconButton(
                                onClick = {
                                    val calendar = Calendar.getInstance()
                                    calendar.add(Calendar.MINUTE, 1) // 默认加1分钟
                                    targetTimeStr =
                                        SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                                            .format(calendar.time)
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Filled.Refresh,
                                    contentDescription = "设置为当前时间+1分钟"
                                )
                            }
                        }
                    )

                    OutlinedTextField(
                        value = requestBody,
                        onValueChange = { requestBody = it },
                        label = { Text(if (validateJsonFormat()) "请求参数" else "格式错误或参数不完整") },
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        isError = !validateJsonFormat()
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (isConfigValid && multiThreadEnabled && fastenAddress) {
                        // 启动抢券
                        onStartGrabbing(
                            requestBody,
                            timerEnabled,
                            targetTimeStr,
                            multiThreadRangeList
                        )
                        val message = buildString {
                            append("抢券任务已启动")
                            if (timerEnabled) append(" | 定时: $targetTimeStr")
                        }
                        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                        setMultiThreadEnabled(false)
                        FailedTokenIndexRecordUtils.clearFailedTokenIndexes(
                            FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION
                        )
                        onDismiss()
                    }
                },
                enabled = isConfigValid && multiThreadEnabled && fastenAddress
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}