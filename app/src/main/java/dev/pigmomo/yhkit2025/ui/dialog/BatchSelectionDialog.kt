package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import dev.pigmomo.yhkit2025.api.model.cart.AddToCartData
import dev.pigmomo.yhkit2025.api.model.cart.BatchDetail
import dev.pigmomo.yhkit2025.api.model.cart.BatchItem
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.PriceUtils

/**
 * 批次选择弹窗
 *
 * @param onDismiss 关闭弹窗回调
 * @param batchData 批次数据
 * @param onBatchSelected 批次选择回调，参数为(批次代码, 数量)
 */
@Composable
fun BatchSelectionDialog(
    onDismiss: () -> Unit,
    batchData: AddToCartData,
    onBatchSelected: (String, Int) -> Unit
) {
    // 选中的批次详情和数量
    var selectedBatch by remember { mutableStateOf<BatchDetail?>(null) }
    var selectedQuantity by remember { mutableIntStateOf(1) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "请选择规格"
            )
        },
        containerColor = dialogContainerColor(),
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 500.dp)
            ) {
                // 商品信息
                ProductInfoSection(batchData)

                Spacer(modifier = Modifier.height(8.dp))

                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f, false),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(batchData.batchitems) { batchItem ->
                        BatchItemSection(
                            batchItem = batchItem,
                            selectedBatch = selectedBatch,
                            onBatchSelect = { batch ->
                                selectedBatch = batch
                                selectedQuantity = 1 // 重置数量
                            }
                        )
                    }
                }

                // 数量选择
                if (selectedBatch != null) {
                    Spacer(modifier = Modifier.height(8.dp))
                    QuantitySelector(
                        quantity = selectedQuantity,
                        maxQuantity = (selectedBatch?.qty ?: 100) / 100,
                        onQuantityChange = { selectedQuantity = it }
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    selectedBatch?.let { batch ->
                        onBatchSelected(batch.skucode, selectedQuantity)
                        onDismiss()
                    }
                },
                enabled = selectedBatch != null
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 商品信息展示区域
 */
@Composable
private fun ProductInfoSection(batchData: AddToCartData) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = cardThemeOverlay()
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 商品图片
            if (batchData.listimgs.isNotEmpty()) {
                AsyncImage(
                    model = batchData.listimgs.first(),
                    contentDescription = null,
                    modifier = Modifier
                        .size(60.dp)
                        .clip(RoundedCornerShape(8.dp)),
                    contentScale = ContentScale.Crop
                )
                Spacer(modifier = Modifier.width(12.dp))
            }

            // 商品信息
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = batchData.title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )

                if (batchData.subtitle.isNotEmpty()) {
                    Text(
                        text = batchData.subtitle,
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                // 价格信息
                batchData.price?.let { price ->
                    Text(
                        text = "¥${PriceUtils.formatPrice(price.value)}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}

/**
 * 批次项目展示区域
 */
@Composable
private fun BatchItemSection(
    batchItem: BatchItem,
    selectedBatch: BatchDetail?,
    onBatchSelect: (BatchDetail) -> Unit
) {
    Column {
        // 批次描述
        if (batchItem.desc.isNotEmpty()) {
            Text(
                text = batchItem.desc,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 2.dp)
            )
        }

        // 批次详情列表
        batchItem.batchlist.forEach { batchDetail ->
            BatchDetailItem(
                batchDetail = batchDetail,
                isSelected = selectedBatch?.skucode == batchDetail.skucode,
                isDefault = batchDetail.skucode == batchItem.defaultBatch,
                onClick = { onBatchSelect(batchDetail) }
            )
        }
    }
}

/**
 * 批次详情项目
 */
@Composable
private fun BatchDetailItem(
    batchDetail: BatchDetail,
    isSelected: Boolean,
    isDefault: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp)
            .clickable { onClick() }
            .then(
                if (isSelected) {
                    Modifier.border(
                        2.dp,
                        MaterialTheme.colorScheme.primary,
                        RoundedCornerShape(8.dp)
                    )
                } else {
                    Modifier
                }
            ),
        colors = cardThemeOverlay()
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 批次图片
            if (batchDetail.image.isNotEmpty()) {
                AsyncImage(
                    model = batchDetail.image,
                    contentDescription = null,
                    modifier = Modifier
                        .size(40.dp)
                        .clip(RoundedCornerShape(6.dp)),
                    contentScale = ContentScale.Crop
                )
                Spacer(modifier = Modifier.width(8.dp))
            }

            // 批次信息
            Column(modifier = Modifier.weight(1f)) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = batchDetail.name,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )

                    // 默认标签
                    if (isDefault) {
                        Text(
                            text = "默认",
                            fontSize = 10.sp,
                            color = Color.White,
                            modifier = Modifier
                                .background(
                                    MaterialTheme.colorScheme.primary,
                                    RoundedCornerShape(4.dp)
                                )
                                .padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }

                // 批次描述
                if (batchDetail.batchdescription.isNotEmpty()) {
                    Text(
                        text = batchDetail.batchdescription,
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                // 价格和库存
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "¥${PriceUtils.formatPrice(batchDetail.price)}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Text(
                        text = "库存: ${batchDetail.qty / 100}",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

/**
 * 数量选择器
 */
@Composable
private fun QuantitySelector(
    quantity: Int,
    maxQuantity: Int,
    onQuantityChange: (Int) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = cardThemeOverlay()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "数量",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )

            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 减少按钮
                QuantityControlButton(
                    text = "-",
                    enabled = quantity > 1,
                    onClick = {
                        if (quantity > 1) {
                            onQuantityChange(quantity - 1)
                        }
                    }
                )

                // 数量显示
                Text(
                    text = quantity.toString(),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )

                // 增加按钮
                QuantityControlButton(
                    text = "+",
                    enabled = quantity < maxQuantity,
                    onClick = {
                        if (quantity < maxQuantity) {
                            onQuantityChange(quantity + 1)
                        }
                    }
                )
            }
        }
    }
}

/**
 * 数量控制按钮组件
 * 统一的加减按钮样式
 */
@Composable
private fun QuantityControlButton(
    text: String,
    enabled: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(24.dp) // 批次选择弹窗中使用稍大的按钮
            .clip(RoundedCornerShape(4.dp))
            .background(
                if (enabled) MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.1f)
            )
            .clickable(enabled = enabled) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = if (enabled) MaterialTheme.colorScheme.primary
            else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
        )
    }
}
