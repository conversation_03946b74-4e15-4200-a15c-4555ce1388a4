package dev.pigmomo.yhkit2025.api.service

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.SignType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 搜索服务类
 * 提供商品搜索相关的API调用方法
 */
class SearchService(requestHelper: RequestHelper) : BaseService(requestHelper) {

    private val tag = "SearchService"
    private val serviceType = requestHelper.getServiceType()

    /**
     * 关键词搜索商品
     * @param keyword 搜索关键词
     * @param ordertype 排序类型，默认为 0
     * @param order 排序方式，默认为 0
     * @param page 页码，默认为 0
     * @param enableCorrection 是否启用纠错，默认为 1
     * @param tabChangeAble 标签是否可变，默认为 0 ,1 及时达，2 快递发货
     * @return 搜索结果响应
     */
    suspend fun keywordSearch(
        keyword: String,
        ordertype: Int = 0,
        order: Int = 0,
        page: Int = 0,
        enableCorrection: Int = 1,
        tabChangeAble: Int = 1,
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        val sellerId = requestHelper.getSellerId()
        val shopId = requestHelper.getShopId()
        if (sellerId.isEmpty() || shopId.isEmpty()) {
            Log.e(tag, "keywordSearch: sellerId or shopId is empty")
            return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
        }

        when (serviceType) {
            "app" -> {
                val commonParams = requestHelper.getAppCommonParams()

                val xyhBizParams = requestHelper.getXyhBizParams()
                    .ifEmpty { RequestConfig.XYH_BIZ_PARAMS_ORIGIN_APP }

                // 构建业务参数
                val businessParams = buildAppBusinessParams().apply {
                    put("ordertype", ordertype)
                    put("order", order)
                    put("page", page)
                    put("keyword", keyword)
                    put("sellerid", sellerId)
                    put("shopid", shopId)
                    put("enableCorrection", enableCorrection)
                    put("tabChangeAble", tabChangeAble)
                    put("elderly", 0)

                    // 可选参数
                    put("abdata", "{\"keyword-search-tab-v1125\":\"2\",\"search-recall-rerank\":\"v2_3\",\"search_algo_search_v7_reRank_level\":\"no_model\",\"category_predict\":\"v2\",\"ngram_version\":\"ngram_v2\",\"search_results_nostock_sku_v8100\":\"0\",\"search_results_coupon_exposed_position\":\"5\",\"participial_phrase\":\"search_recall_newseg\",\"keyword_search_abt\":\"3\",\"search_public_ab\":\"level_test\",\"search-tag-filter-v855\":\"1\",\"search_v7_reRank\":\"search_rank_normal\",\"app_searchResult_billboard_abt_9110\":\"1\"}")
                }

                // 构建URL
                val urlWithParams = buildAppApiUrl(
                    RequestConfig.Path.KEYWORD_SEARCH_PATH,
                    businessParams,
                    commonParams
                )

                val needSignStr = urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                    .replace("=", "")
                    .replace("&", "")

                val needSignStrFixed = needSignStr.replace("%7B%22keyword-search-tab-v1125%22%3A%222%22%2C%22search-recall-rerank%22%3A%22v2_3%22%2C%22search_algo_search_v7_reRank_level%22%3A%22no_model%22%2C%22category_predict%22%3A%22v2%22%2C%22ngram_version%22%3A%22ngram_v2%22%2C%22search_results_nostock_sku_v8100%22%3A%220%22%2C%22search_results_coupon_exposed_position%22%3A%225%22%2C%22participial_phrase%22%3A%22search_recall_newseg%22%2C%22keyword_search_abt%22%3A%223%22%2C%22search_public_ab%22%3A%22level_test%22%2C%22search-tag-filter-v855%22%3A%221%22%2C%22search_v7_reRank%22%3A%22search_rank_normal%22%2C%22app_searchResult_billboard_abt_9110%22%3A%221%22%7D", "{\"keyword-search-tab-v1125\":\"2\",\"search-recall-rerank\":\"v2_3\",\"search_algo_search_v7_reRank_level\":\"no_model\",\"category_predict\":\"v2\",\"ngram_version\":\"ngram_v2\",\"search_results_nostock_sku_v8100\":\"0\",\"search_results_coupon_exposed_position\":\"5\",\"participial_phrase\":\"search_recall_newseg\",\"keyword_search_abt\":\"3\",\"search_public_ab\":\"level_test\",\"search-tag-filter-v855\":\"1\",\"search_v7_reRank\":\"search_rank_normal\",\"app_searchResult_billboard_abt_9110\":\"1\"}")

                // 生成签名
                val sign = requestHelper.generateSign(needSignStrFixed, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "keywordSearch: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val headers = buildAppStandardHeaders(xyhBizParams, false, false)

                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                val commonParams = requestHelper.getMiniProgramCommonParams()

                val xyhBizParams = requestHelper.getXyhBizParams()
                    .ifEmpty { RequestConfig.XYH_BIZ_PARAMS_ORIGIN_MINI_PROGRAM }

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams().apply {
                    put("ordertype", ordertype)
                    put("order", order)
                    put("page", page)
                    put("keyword", keyword)
                    put("sellerid", sellerId)
                    put("shopid", shopId)
                    put("enableCorrection", enableCorrection)
                    put("tabChangeAble", tabChangeAble)
                    put("elderly", 0)
                    put("memberid", requestHelper.getUid())
                    put("distinctId", requestHelper.getUid())

                    put("abdata", "{\"keyword-search-tab-v1125\":\"2\",\"search-recall-rerank\":\"v2_3\",\"search_algo_search_v7_reRank_level\":\"no_model\",\"category_predict\":\"v2\",\"ngram_version\":\"ngram_v2\",\"search_results_nostock_sku_v8100\":\"0\",\"search_results_coupon_exposed_position\":\"5\",\"participial_phrase\":\"search_recall_newseg\",\"keyword_search_abt\":\"3\",\"search_public_ab\":\"level_test\",\"search-tag-filter-v855\":\"1\",\"search_v7_reRank\":\"search_rank_normal\",\"app_searchResult_billboard_abt_9110\":\"1\"}")
                }

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.KEYWORD_SEARCH_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    "",
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }
}
