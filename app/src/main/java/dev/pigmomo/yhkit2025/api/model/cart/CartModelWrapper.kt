package dev.pigmomo.yhkit2025.api.model.cart

import kotlin.collections.get

/**
 * 购物车模型包装类
 * 用于统一处理不同类型的购物车模型
 *
 * @param data 模型数据（根据modelType不同，可能是DeliveryInfo、PromotionInfo或ProductItem）
 * @param modelType 模型类型，参考CartModelTypes常量
 */
data class CartModelWrapper(
    val data: Any? = null,
    val modelType: Int = 0
) {
    /**
     * 获取配送信息
     * 当modelType为DELIVERY_INFO(2)时有效
     */
    fun getDelivery(): CartDelivery? {
        return if (modelType == CartModelTypes.DELIVERY_INFO && data is Map<*, *>) {
            try {
                val image = (data["image"] as? String) ?: ""
                val name = (data["name"] as? String) ?: ""
                val color = (data["color"] as? String) ?: ""
                val deliveryDesc = (data["deliveryDesc"] as? String) ?: ""

                CartDelivery(
                    image = image,
                    name = name,
                    color = color,
                    deliveryDesc = deliveryDesc
                )
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }

    /**
     * 获取商品项信息
     * 当modelType为PRODUCT_ITEM(5)时有效
     */
    fun getProduct(): Product? {
        return if (modelType == CartModelTypes.PRODUCT_ITEM && data is Map<*, *>) {
            try {
                val id = (data["id"] as? String) ?: ""
                val originalskucode = (data["originalskucode"] as? String) ?: ""
                val title = (data["title"] as? String) ?: ""
                val addTime = (data["addTime"] as? Number)?.toLong() ?: 0
                val action = (data["action"] as? String) ?: ""

                // 解析价格信息
                val priceMap = (data["price"] as? Map<*, *>)
                val price = if (priceMap != null) {
                    ProductPrice(
                        total = (priceMap["total"] as? Number)?.toInt() ?: 0,
                        value = (priceMap["value"] as? Number)?.toInt() ?: 0,
                        market = (priceMap["market"] as? Number)?.toInt() ?: 0,
                        priceKind = (priceMap["priceKind"] as? String) ?: ""
                    )
                } else null

                // 解析实际价格信息
                val actualPriceMap = (data["actualPrice"] as? Map<*, *>)
                val actualPrice = if (actualPriceMap != null) {
                    ProductActualPrice(
                        priceDesc = (actualPriceMap["priceDesc"] as? String) ?: "",
                        displayMoney = (actualPriceMap["displayMoney"] as? Number)?.toInt() ?: 0
                    )
                } else null

                // 解析规格信息
                val specMap = (data["spec"] as? Map<*, *>)
                val spec = if (specMap != null) {
                    ProductSpec(
                        desc = (specMap["desc"] as? String) ?: ""
                    )
                } else null

                // 解析标签列表
                val taglistData = (data["taglist"] as? List<*>) ?: emptyList<Any>()
                val taglist = taglistData.mapNotNull { tagData ->
                    if (tagData is Map<*, *>) {
                        TagItem(
                            type = (tagData["type"] as? String) ?: "",
                            text = (tagData["text"] as? String) ?: "",
                            uitype = (tagData["uitype"] as? String) ?: "",
                            sort = (tagData["sort"] as? Number)?.toInt() ?: 0
                        )
                    } else null
                }

                // 解析标题标签列表
                val titletagsData = (data["titletags"] as? List<*>) ?: emptyList<Any>()
                val titletags = titletagsData.mapNotNull { titleTagData ->
                    if (titleTagData is Map<*, *>) {
                        TitleTagItem(
                            type = (titleTagData["type"] as? String) ?: "",
                            text = (titleTagData["text"] as? String) ?: "",
                            uitype = (titleTagData["uitype"] as? String) ?: "",
                            sort = (titleTagData["sort"] as? Number)?.toInt() ?: 0
                        )
                    } else null
                }

                // 解析限购信息列表
                val restrictsData = (data["restricts"] as? List<*>) ?: emptyList<Any>()
                val restricts = restrictsData.mapNotNull { restrictData ->
                    if (restrictData is Map<*, *>) {
                        ProductRestrict(
                            limit = (restrictData["limit"] as? Number)?.toInt() ?: 0,
                            restricttype = (restrictData["restricttype"] as? Number)?.toInt() ?: 0
                        )
                    } else null
                }

                val goodstag = (data["goodstag"] as? String) ?: ""
                val goodstagid = (data["goodstagid"] as? Number)?.toInt() ?: 0

                val bundlepromocode = (data["bundlepromocode"] as? String) ?: ""
                val orderremark = (data["orderremark"] as? String) ?: ""

                // 创建ProductItem对象
                Product(
                    id = id,
                    originalskucode = originalskucode,
                    title = title,
                    addTime = addTime,
                    action = action,
                    price = price,
                    actualPrice = actualPrice,
                    available = (data["available"] as? Number)?.toInt() ?: 0,
                    pattern = (data["pattern"] as? String) ?: "",
                    spec = spec,
                    pickself = (data["pickself"] as? Number)?.toInt() ?: 0,
                    sellercategory = (data["sellercategory"] as? String) ?: "",
                    lackOrExchangeNotShow = (data["lackOrExchangeNotShow"] as? Boolean) ?: false,
                    isbulkitem = (data["isbulkitem"] as? Number)?.toInt() ?: 0,
                    showSimilar = (data["showSimilar"] as? Boolean) == true,
                    grossWeight = (data["grossWeight"] as? Number)?.toDouble() ?: 0.0,
                    performanceHourHour = (data["performanceHourHour"] as? Boolean) == true,
                    imgurl = (data["imgurl"] as? String) ?: "",
                    subtitle = (data["subtitle"] as? String) ?: "",
                    stocknum = (data["stocknum"] as? Number)?.toInt() ?: 0,
                    fewstockremark = (data["fewstockremark"] as? String) ?: "",
                    num = (data["num"] as? Number)?.toInt() ?: 0,
                    changenum = (data["changenum"] as? Number)?.toInt() ?: 0,
                    selectstate = (data["selectstate"] as? Number)?.toInt() ?: 0,
                    originalselectstate = (data["originalselectstate"] as? Boolean) == true,
                    isdelivery = (data["isdelivery"] as? Number)?.toInt() ?: 0,
                    sellerid = (data["sellerid"] as? String) ?: "",
                    taglist = taglist,
                    batchdescription = (data["batchdescription"] as? String) ?: "",
                    deliverytimedesc = (data["deliverytimedesc"] as? String) ?: "",
                    batchcode = (data["batchcode"] as? String) ?: "",
                    skusaletype = (data["skusaletype"] as? Number)?.toInt() ?: 0,
                    skutype = (data["skutype"] as? Number)?.toInt() ?: 0,
                    deliverytime = (data["deliverytime"] as? Number)?.toLong() ?: 0,
                    isperformancehourhour = (data["isperformancehourhour"] as? Boolean) == true,
                    titletags = titletags,
                    pricechange = (data["pricechange"] as? String) ?: "",
                    restricts = restricts,
                    canNotBuy = (data["canNotBuy"] as? Boolean) == true,
                    productRemarks = (data["productRemarks"] as? List<*>)?.mapNotNull { item ->
                        (item as? Map<*, *>)?.let { remarkMap ->
                            ProductRemark(
                                amount = (remarkMap["amount"] as? Number)?.toInt() ?: 0
                            )
                        }
                    } ?: emptyList(),
                    categoryId = (data["categoryId"] as? String) ?: "",
                    saleCategoryList = (data["saleCategoryList"] as? List<*>)?.filterNotNull()
                        ?: emptyList(),
                    goodstagid = goodstagid,
                    bundlepromocode = bundlepromocode,
                    orderremark = orderremark,
                    goodstag = goodstag
                )
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        } else {
            null
        }
    }

    /**
     * 获取店铺名称
     * 当modelType为SHOP_NAME(6)时有效
     */
    fun getShopName(): String? {
        return if (modelType == CartModelTypes.SHOP_NAME && data is String) {
            data
        } else {
            null
        }
    }
}