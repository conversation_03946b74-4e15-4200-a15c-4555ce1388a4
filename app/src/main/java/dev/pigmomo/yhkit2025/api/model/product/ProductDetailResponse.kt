package dev.pigmomo.yhkit2025.api.model.product

/**
 * 商品详情响应数据模型
 */
data class ProductDetailResponse(
    val code: Int,
    val message: String,
    val data: ProductDetailData?,
    val now: Long?
)

/**
 * 商品详情数据
 */
data class ProductDetailData(
    val batchFlagToBoolean: Boolean?,
    val id: String?,
    val shopid: String?,
    val throughGroupId: String?,
    val mainimgs: List<MainImage>?,
    val categorys: List<String>?,
    val picdetail: List<String>?,
    val skuImages: List<SkuImage>?,
    val title: String?,
    val subtitle: String?,
    val stock: StockInfo?,
    val price: PriceInfo?,
    val spec: List<SpecInfo>?,
    val place: List<PlaceInfo>?,
    val action: String?,
    val comment: CommentInfo?,
    val promotion: PromotionInfo?,
    val marketing: MarketingInfo?,
    val seller: SellerInfo?,
    val skuStatus: SkuStatusInfo?,
    val productType: Int?,
    val smallImg: String?,
    val expiration: Int?,
    val restricts: List<String>?,
    val isdelivery: Int?,
    val cityname: String?,
    val cityid: Int?,
    val secondSellercategory: String?,
    val secondSellercategoryVo: SecondSellerCategoryVo?,
    val balancerefund: Int?,
    val commercialid: Int?,
    val status: Int?,
    val isjoinnewpriceactivity: Int?,
    val sapcategoryid: String?,
    val titletaglist: List<TitleTagItem>?,
    val arrivalnotice: Int?,
    val batchflag: Int?,
    val batchskucode: String?,
    val batchtimedes: String?,
    val dateinfovo: DateInfoVo?,
    val skubatchpopdes: SkuBatchPopDes?,
    val skusaletype: Int?,
    val skuservicedes: SkuServiceDes?,
    val popskuservicedes: SkuServiceDes?,
    val standarddescup: List<AttributeInfo>?,
    val standarddescdown: List<AttributeInfo>?,
    val attributespopup: AttributesPopup?,
    val skutype: Int?,
    val favoriteButton: FavoriteButton?,
    val showModule: String?,
    val isGiftBar: Int?,
    val giftShareImg: String?,
    val barrage: BarrageInfo?,
    val categoryEntryVo: CategoryEntryVo?,
    val bigSalesPromotionVo: BigSalesPromotionVo?,
    val statusFlags: Int?
)

/**
 * 主图片
 */
data class MainImage(
    val imgurl: String?
)

/**
 * SKU图片
 */
data class SkuImage(
    val scalable: Int?,
    val imageUrl: String?
)

/**
 * 库存信息
 */
data class StockInfo(
    val desc: String?,
    val count: Int?,
    val minNum: Int?,
    val minQtyNum: Int?
)

/**
 * 价格信息
 */
data class PriceInfo(
    val value: Int?,
    val market: Int?,
    val flag: String?,
    val pricetype: String?,
    val marketflag: String?,
    val showprice: Int?,
    val canBuy: Int?,
    val spec: String?,
    val superprice: Int?,
    val tariff: Int?,
    val priceDesc: PriceDesc?
)

/**
 * 价格描述
 */
data class PriceDesc(
    val type: Int?
)

/**
 * 规格信息
 */
data class SpecInfo(
    val pid: String?
)

/**
 * 产地信息
 */
data class PlaceInfo(
    val prompt: String?,
    val value: String?
)

/**
 * 评论信息
 */
data class CommentInfo(
    val realPositiveRate: Double?,
    val title: String?,
    val count: Int?,
    val commentCount: String?,
    val positiveRate: String?,
    val action: String?,
    val commentTagInfos: List<CommentTagInfo>?
)

/**
 * 评论标签信息
 */
data class CommentTagInfo(
    val id: Int?,
    val name: String?,
    val count: Int?,
    val unitCount: String?
)

/**
 * 促销信息
 */
data class PromotionInfo(
    val promoactionurl: String?,
    val promonames: String?,
    val promoshownum: String?,
    val promodesc: String?,
    val promos: List<PromoItem>?,
    val couponkinds: List<CouponKind>?,
    val coupons: List<CouponItem>?,
    val receivecoupons: List<ReceiveCoupon>?,
    val pendingcount: Int?,
    val taglist: List<TagItem>?,
    val promotionpopvo: PromotionPopVo?
)

/**
 * 促销项目
 */
data class PromoItem(
    val canBeCombined: Boolean?,
    val name: String?,
    val subname: String?,
    val promoactionurl: String?,
    val tag: String?,
    val canCombineNonCashCoupon: Boolean?,
    val code: String?,
    val titletag: TitleTag?,
    val originalPromotionSubType: String?
)

/**
 * 标题标签
 */
data class TitleTag(
    val type: String?,
    val text: String?,
    val state: Long?,
    val sort: Long?,
    val benefit: Long?,
    val threshold: Long?
)

/**
 * 优惠券种类
 */
data class CouponKind(
    val name: String?,
    val catalog: Int?,
    val couponLatitude: String?
)

/**
 * 优惠券项目
 */
data class CouponItem(
    val catalog: Int?,
    val name: String?,
    val amount: Int?,
    val realm: String?,
    val sellers: List<String>?,
    val promotionShops: List<PromotionShop>?,
    val label: Int?,
    val couponTimeType: Int?,
    val loginType: String?,
    val activityShops: List<String>?,
    val orderLimits: List<Int>?,
    val activityStatus: String?,
    val activityStartTime: Long?,
    val activityEndTime: Long?,
    val showDescription: String?,
    val shopCouponSaleChanelLabel: Map<String, Int>?,
    val couponImageUrl: String?,
    val deliveryLimits: List<Int>?,
    val promotioncode: String?,
    val desc: String?,
    val orderminamount: Int?,
    val minmemberlevel: Int?,
    val actionurl: String?,
    val realmid: Int?,
    val conditiondesc: String?,
    val shoprealm: String?,
    val descriptions: List<String>?,
    val scope: String?,
    val usescenario: Int?,
    val useprompt: String?,
    val iscurrentavailable: Int?,
    val salechannel: Long?,
    val taglist: List<TagItem>?,
    val newcustomer: Int?,
    val delaydays: Int?,
    val expirationdays: Int?,
    val couponlatitude: String?,
    val shopsellers: List<String>?,
    val isrecommended: Int?,
    val sentcount: Int?,
    val canapply: Int?,
    val isavailable: Int?,
    val currentmemberlevel: Int?,
    val receivedbefore: Int?,
    val sendperioddesc: String?,
    val limitcountpermember: Int?,
    val remaincount: Int?,
    val isappmemberexclusivecoupon: Boolean?
)

/**
 * 标题标签项目
 */
data class TitleTagItem(
    val realType: String?,
    val type: String?,
    val imgurl: String?,
    val state: Long?,
    val sort: Long?,
    val benefit: Long?,
    val threshold: Long?,
    val extendInfo: ExtendInfo?
)

/**
 * 扩展信息
 */
data class ExtendInfo(
    val tagHeight: Int?,
    val tagWidth: Int?
)


