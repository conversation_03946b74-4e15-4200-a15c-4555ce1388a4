package dev.pigmomo.yhkit2025.api.model.cart

/**
 * 商品项数据类
 * 对应购物车中modelType=5的商品信息
 */
data class Product(
    val id: String = "",                      // 商品ID
    val originalskucode: String = "",         // 原始SKU码
    val title: String = "",                   // 商品标题
    val addTime: Long = 0,                    // 添加时间
    val action: String = "",                  // 操作链接
    val price: ProductPrice? = null,          // 价格信息
    val actualPrice: ProductActualPrice? = null, // 实际价格信息
    val available: Int = 0,                   // 是否可用
    val pattern: String = "",                 // 模式
    val spec: ProductSpec? = null,            // 规格信息
    val pickself: Int = 0,                    // 自提标志
    val sellercategory: String = "",          // 卖家分类
    val lackOrExchangeNotShow: Boolean = false, // 缺货或换货不显示
    val isbulkitem: Int = 0,                  // 是否批量商品
    val goodstag: String = "",                // 商品标签
    val restrict: ProductRestrict? = null,    // 限购信息
    val showSimilar: Boolean = false,         // 显示类似商品
    val addBuyQty: Int = 0,                   // 加购数量
    val expirationDate: Int = 0,              // 过期日期
    val grossWeight: Double = 0.0,            // 毛重
    val performanceHourHour: Boolean = false, // 小时达标志
    val imgurl: String = "",                  // 图片URL
    val subtitle: String = "",                // 副标题
    val stocknum: Int = 0,                    // 库存数量
    val fewstockremark: String = "",          // 缺货备注
    val num: Int = 0,                         // 购买数量
    val changenum: Int = 0,                   // 变更数量
    val selectstate: Int = 0,                 // 选择状态
    val originalselectstate: Boolean = false, // 原始选择状态
    val isdelivery: Int = 0,                  // 是否配送
    val sellerid: String = "",                // 卖家ID
    val producttags: List<String> = emptyList(), // 商品标签列表
    val taglist: List<TagItem> = emptyList(), // 标签列表
    val batchdescription: String = "",        // 批次描述
    val deliverytimedesc: String = "",        // 配送时间描述
    val batchcode: String = "",               // 批次代码
    val skusaletype: Int = 0,                 // SKU销售类型
    val titledescription: String = "",        // 标题描述
    val skutype: Int = 0,                     // SKU类型
    val isperformancehourhour: Boolean = false, // 是否小时达
    val titletags: List<TitleTagItem> = emptyList(),   // 标题标签
    val pricechange: String = "",              // 价格变动
    val isseckill: Int = 0,                   // 是否秒杀
    val splitLine: Int = 0,                   // 分隔线
    val cartSeckillDetailVO: SeckillDetail? = null, // 秒杀详情
    val canNotBuy: Boolean = false,           // 是否不可购买
    val productRemarks: List<ProductRemark> = emptyList(), // 商品备注
    val categoryId: String = "",              // 分类ID
    val saleCategoryList: List<Any> = emptyList(), // 销售分类列表
    val restricts: List<ProductRestrict>? = null, // 限购信息列表
    val couponIds: List<String>? = null,       // 优惠券ID列表
    val deliverytime: Long,                    // 配送时间
    val goodstagid: Int = 0,                  // 商品标签ID
    val bundlepromocode: String = "",         // 促销码
    val orderremark: String = ""               // 商品备注
)

/**
 * 商品价格数据类
 */
data class ProductPrice(
    val total: Int = 0,          // 总价(分)
    val value: Int = 0,          // 当前价格(分)
    val market: Int = 0,         // 市场价格(分)
    val priceKind: String = ""   // 价格类型(如"seckill"表示秒杀价)
)

/**
 * 商品实际价格数据类
 */
data class ProductActualPrice(
    val priceDesc: String = "", // 价格描述
    val displayMoney: Int = 0   // 显示价格(分)
)

/**
 * 商品规格数据类
 */
data class ProductSpec(
    val desc: String = ""        // 规格描述
)

/**
 * 商品限购数据类
 */
data class ProductRestrict(
    val limit: Int = 0,          // 限购数量
    val restricttype: Int = 0    // 限购类型
)

/**
 * 标签项数据类
 */
data class TagItem(
    val type: String = "",      // 标签类型
    val text: String = "",      // 标签文本
    val uitype: String = "",    // UI类型
    val sort: Int = 0           // 排序值
)

/**
 * 标题标签项数据类
 */
data class TitleTagItem(
    val type: String = "",      // 标签类型
    val text: String = "",      // 标签文本
    val uitype: String = "",    // UI类型
    val sort: Int = 0           // 排序值
)

/**
 * 秒杀详情数据类
 */
data class SeckillDetail(
    val systemTime: Long = 0,    // 系统时间
    val endTo: Long = 0          // 结束时间
)

/**
 * 商品备注数据类
 */
data class ProductRemark(
    val amount: Int = 0          // 数量
)