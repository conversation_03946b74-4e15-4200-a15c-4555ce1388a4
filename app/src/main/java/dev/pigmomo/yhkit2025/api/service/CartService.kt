package dev.pigmomo.yhkit2025.api.service

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.SignType
import dev.pigmomo.yhkit2025.api.model.cart.CartItem
import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.api.model.user.AddressItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import java.util.UUID
import kotlin.collections.get
import kotlin.math.abs

/**
 * 购物车服务类
 * 提供购物车相关的API调用方法
 */
class CartService(requestHelper: RequestHelper) : BaseService(requestHelper) {

    private val tag = "CartService"

    private val serviceType = requestHelper.getServiceType()

    /**
     * 获取购物车列表
     * @param addressId 地址ID
     * @return 购物车列表响应结果
     */
    suspend fun getAllCart(addressId: String): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val commonParams = requestHelper.getAppCommonParams()

                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getAllCart: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                // 解析X-YH-Biz-Params获取gov和gib参数
                val xyhBizParamsArray = xyhBizParams.split("&")
                val gov = xyhBizParamsArray[0].split("=")[1]
                val gib = xyhBizParamsArray[1].split("=")[1]
                if (gov.isEmpty() || gib.isEmpty()) {
                    Log.e(tag, "getAllCart: gov or gib is empty")
                    return@withContext RequestResult.Error(Exception("gov or gib is empty"))
                }

                // 添加AB测试数据
                val businessParams = buildAppBusinessParams()
                businessParams["abdata"] = "{\"cart_new_people_850\":\"B\"}"

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(RequestConfig.Path.CART_LIST_PATH, businessParams, commonParams)

                // 构建请求体
                val requestBody = """
                {
                "addressid":"$addressId",
                "gjxvodji":{
                    "gib":"$gib",
                    "gvo":"$gov"
                },
                "isFromCart":false,
                "pagesource":"",
                "source":1,
                "sourcePage":"",
                "cid":"${requestHelper.getChannel()}",
                "mid":"AppChannel",
                "sid":"Default"
                }
                """.trimIndent().replace("\n", "").replace(" ", "")

                // 生成签名URL
                val needSignStr = urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                    .replace("=", "").replace("&", "") + requestBody

                // 替换abdata编码
                val needSignStrFixed = needSignStr.replace(
                    "%7B%22cart_new_people_850%22%3A%22B%22%7D",
                    "{\"cart_new_people_850\":\"B\"}"
                )

                Log.d(tag, "needSignStrFixed: $needSignStrFixed")

                // 生成签名
                val sign =
                    requestHelper.generateSign(needSignStrFixed, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "getAllCart: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "getAllCart: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.CART_LIST_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 构建请求体
                val requestBody = """
                {
                "addressid":"$addressId",
                "isFromCart":false,
                "pagesource":"",
                "source":1,
                "sourcePage":""
                }
                """.trimIndent().replace("\n", "").replace(" ", "")

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getAllCart: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 修改购物车
     * @param operation 操作类型（"selectstate"或"num"）
     * @param selectedAddress 选中的地址
     * @param changeValue 修改的值
     * @param product 商品模型
     * @return 修改购物车响应结果
     */
    suspend fun changeCart(
        operation: String,
        selectedAddress: AddressItem,
        changeValue: String,
        product: Product
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "cartChange: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                val commonParams = requestHelper.getAppCommonParams()

                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "cartChange: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                // 构建业务参数
                val businessParams = buildAppBusinessParams()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.CART_CHANGE_PATH,
                        businessParams,
                        commonParams
                    )

                //selectedAddress
                val addressid = selectedAddress.id
                val location = selectedAddress.location
                val lat = location?.lat
                val lng = location?.lng

                //operatetype 1:增加 2:减少
                val operatetype = if (changeValue.toInt() < 0) {
                    2
                } else {
                    1
                }

                //cartModel
                val id = product.id
                val num = product.num
                val selectstate = product.selectstate
                val orderremarkStr =
                    if (product.orderremark.isNotEmpty()) "\"orderremark\":\"${product.orderremark}\"," else ""
                val productRemarks = if (product.productRemarks.isNotEmpty()) {
                    "[" + product.productRemarks.joinToString(",") { "{\"amount\":${if (operation == "num" && operatetype == 1) it.amount + 1 else if (operation == "num") it.amount - 1 else it.amount}}" } + "]"
                } else {
                    "[]"
                }
                val goodstagid = product.goodstagid

                // 构建请求体 - 根据操作类型构建不同的数据结构
                val requestBody = when (operation) {
                    "selectstate" -> {
                        """{"addressid":"$addressid","isFromCart":false,"location":{"lat":"$lat","lng":"$lng","_uuid":"${UUID.randomUUID()}"},"pagesource":"","seller":[{"isFromCart":false,"products":[{"goodstagid":$goodstagid,"id":"$id","num":0,"operatetype":0,$orderremarkStr"productRemarks":$productRemarks,"selectstate":$changeValue,"_uuid":"${UUID.randomUUID()}"}],"sellerid":"$sellerId","shopid":"$shopId"}],"source":1,"sourcePage":"","cid":"${requestHelper.getChannel()}","mid":"AppChannel","sid":"Default"}"""
                    }

                    "num" -> {
                        """{"addressid":"$addressid","isFromCart":false,"location":{"lat":"$lat","lng":"$lng","_uuid":"${UUID.randomUUID()}"},"pagesource":"","seller":[{"isFromCart":false,"products":[{"goodstagid":$goodstagid,"id":"$id","num":${
                            abs(
                                changeValue.toInt()
                            ) * 100
                        },"operatetype":$operatetype,$orderremarkStr"productRemarks":$productRemarks,"selectstate":$selectstate,"_uuid":"${UUID.randomUUID()}"}],"sellerid":"$sellerId","shopid":"$shopId"}],"source":1,"sourcePage":"","cid":"${requestHelper.getChannel()}","mid":"AppChannel","sid":"Default"}"""
                    }

                    else -> {
                        """"""
                    }
                }

                val needSignStr =
                    urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                        .replace("=", "")
                        .replace("&", "") + requestBody

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "cartChange: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "cartChange: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.CART_CHANGE_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                //selectedAddress
                val addressid = selectedAddress.id
                val location = selectedAddress.location
                val lat = location?.lat
                val lng = location?.lng

                //operatetype 1:增加 2:减少
                val operatetype = if (changeValue.toInt() < 0) {
                    2
                } else {
                    1
                }

                //cartModel
                val id = product.id
                val num = product.num
                val selectstate = product.selectstate
                val orderremarkStr =
                    if (product.orderremark.isNotEmpty()) "\"orderremark\":\"${product.orderremark}\"," else ""
                val productRemarks = if (product.productRemarks.isNotEmpty()) {
                    "[" + product.productRemarks.joinToString(",") { "{\"amount\":${if (operation == "num" && operatetype == 1) it.amount + 1 else if (operation == "num") it.amount - 1 else it.amount}}" } + "]"
                } else {
                    "[]"
                }
                val goodstagid = product.goodstagid

                // 构建请求体 - 根据操作类型构建不同的数据结构
                val requestBody = when (operation) {
                    "selectstate" -> {
                        """{"addressid":"$addressid","isFromCart":false,"location":{"lat":"$lat","lng":"$lng","_uuid":"${UUID.randomUUID()}"},"pagesource":"","seller":[{"isFromCart":false,"products":[{"goodstagid":$goodstagid,"id":"$id","num":0,"operatetype":0,$orderremarkStr"productRemarks":$productRemarks,"selectstate":$changeValue,"_uuid":"${UUID.randomUUID()}"}],"sellerid":"$sellerId","shopid":"$shopId"}],"source":1,"sourcePage":""}"""
                    }

                    "num" -> {
                        """{"addressid":"$addressid","isFromCart":false,"location":{"lat":"$lat","lng":"$lng","_uuid":"${UUID.randomUUID()}"},"pagesource":"","seller":[{"isFromCart":false,"products":[{"goodstagid":$goodstagid,"id":"$id","num":${
                            abs(
                                changeValue.toInt()
                            ) * 100
                        },"operatetype":$operatetype,$orderremarkStr"productRemarks":$productRemarks,"selectstate":$selectstate,"_uuid":"${UUID.randomUUID()}"}],"sellerid":"$sellerId","shopid":"$shopId"}],"source":1,"sourcePage":""}"""
                    }

                    else -> {
                        """"""
                    }
                }

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "cartChange: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 删除购物车中的商品
     * @param cartData 购物车数据
     * @param selectedAddress 选中的地址
     * @return 删除购物车商品响应结果
     */
    suspend fun clearCart(
        cartData: Any,
        selectedAddress: AddressItem,
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "cartClear: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                val commonParams = requestHelper.getAppCommonParams()

                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "cartClear: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                // 构建业务参数
                val businessParams = buildAppBusinessParams()
                businessParams["abdata"] = "{\"cart_new_people_850\":\"B\"}"

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(RequestConfig.Path.CART_CLEAR_PATH, businessParams, commonParams)

                val addressid = selectedAddress.id
                val location = selectedAddress.location
                val lat = location?.lat
                val lng = location?.lng

                val requestBody = when (cartData) {
                    is Product -> {
                        val bundlepromocode =
                            if (cartData.bundlepromocode.isNotEmpty()) "\"bundlepromocode\":\"${cartData.bundlepromocode}\"," else ""
                        val orderremark =
                            if (cartData.orderremark.isNotEmpty()) "\"orderremark\":\"${cartData.orderremark}\"," else ""
                        val goodstagid =
                            if (cartData.goodstagid != 0) cartData.goodstagid else 0
                        val id = cartData.id

                        """{"addressid":"$addressid","isFromCart":false,"location":{"lat":"$lat","lng":"$lng","_uuid":"${UUID.randomUUID()}"},"pagesource":"","seller":[{"isFromCart":false,"products":[{$bundlepromocode"goodstagid":$goodstagid,"id":"$id","num":0,$orderremark"operatetype":2,"selectstate":0,"_uuid":"${UUID.randomUUID()}"}],"sellerid":"$sellerId","shopid":"$shopId"}],"source":1,"sourcePage":"","cid":"${requestHelper.getChannel()}","mid":"AppChannel","sid":"Default"}"""
                    }

                    is List<*> -> {
                        val sellerJSONArray = JSONArray()

                        cartData.forEachIndexed { _, currentCart ->
                            val cartItem = currentCart as CartItem
                            val currentShopid = cartItem.shopid
                            val currentSellerid = cartItem.seller?.id

                            val productsJSONArray = JSONArray()

                            val cartModels = cartItem.cartModels
                            cartModels.forEach { cartModel ->
                                if (cartModel.modelType == 5) {
                                    val data = cartModel.data as Map<*, *>
                                    val bundlepromocode =
                                        if (data.containsKey("bundlepromocode") && data["bundlepromocode"] != null)
                                            "\"bundlepromocode\":\"${data["bundlepromocode"]}\","
                                        else ""
                                    val orderremark =
                                        if (data.containsKey("orderremark") && data["orderremark"] != null)
                                            "\"orderremark\":\"${data["orderremark"]}\","
                                        else ""
                                    val goodstagid =
                                        (data["goodstagid"]?.toString()?.toFloatOrNull()
                                            ?: 0f).toInt()
                                    val id = data["id"]?.toString() ?: ""

                                    productsJSONArray.put("""{$bundlepromocode"goodstagid":$goodstagid,"id":"$id","num":0,$orderremark"operatetype":2,"selectstate":0,"_uuid":"${UUID.randomUUID()}"}""")
                                }
                            }

                            sellerJSONArray.put("""{"isFromCart":false,"products":$productsJSONArray,"sellerid":"$currentSellerid","shopid":"$currentShopid"}""")
                        }

                        val sellerStr =
                            sellerJSONArray.toString().replace("\\", "").replace("\"{", "{")
                                .replace("}\"", "}")

                        """{"addressid":"$addressid","isFromCart":false,"location":{"lat":"$lat","lng":"$lng","_uuid":"${UUID.randomUUID()}"},"pagesource":"","seller":$sellerStr,"source":1,"sourcePage":"","cid":"${requestHelper.getChannel()}","mid":"AppChannel","sid":"Default"}"""
                    }

                    else -> {
                        """"""
                    }
                }

                val needSignStr =
                    (urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                        .replace("=", "")
                        .replace("&", "") + requestBody).replace(
                        "%7B%22cart_new_people_850%22%3A%22B%22%7D",
                        "{\"cart_new_people_850\":\"B\"}"
                    )

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "cartClear: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "cartClear: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.CART_CLEAR_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                val addressid = selectedAddress.id
                val location = selectedAddress.location
                val lat = location?.lat
                val lng = location?.lng

                val requestBody = when (cartData) {
                    is Product -> {
                        val bundlepromocode =
                            if (cartData.bundlepromocode.isNotEmpty()) "\"bundlepromocode\":\"${cartData.bundlepromocode}\"," else ""
                        val orderremark =
                            if (cartData.orderremark.isNotEmpty()) "\"orderremark\":\"${cartData.orderremark}\"," else ""
                        val goodstagid =
                            if (cartData.goodstagid != 0) cartData.goodstagid else 0
                        val id = cartData.id

                        """{"addressid":"$addressid","isFromCart":false,"location":{"lat":"$lat","lng":"$lng","_uuid":"${UUID.randomUUID()}"},"pagesource":"","seller":[{"isFromCart":false,"products":[{$bundlepromocode"goodstagid":$goodstagid,"id":"$id","num":0,$orderremark"operatetype":2,"selectstate":0,"_uuid":"${UUID.randomUUID()}"}],"sellerid":"$sellerId","shopid":"$shopId"}],"source":1,"sourcePage":""}"""
                    }

                    is List<*> -> {
                        val sellerJSONArray = JSONArray()

                        cartData.forEachIndexed { _, currentCart ->
                            val cartItem = currentCart as CartItem
                            val currentShopid = cartItem.shopid
                            val currentSellerid = cartItem.seller?.id

                            val productsJSONArray = JSONArray()

                            val cartModels = cartItem.cartModels
                            cartModels.forEach { cartModel ->
                                if (cartModel.modelType == 5) {
                                    val data = cartModel.data as Map<*, *>
                                    val bundlepromocode =
                                        if (data.containsKey("bundlepromocode") && data["bundlepromocode"] != null)
                                            "\"bundlepromocode\":\"${data["bundlepromocode"]}\","
                                        else ""
                                    val orderremark =
                                        if (data.containsKey("orderremark") && data["orderremark"] != null)
                                            "\"orderremark\":\"${data["orderremark"]}\","
                                        else ""
                                    val goodstagid =
                                        (data["goodstagid"]?.toString()?.toFloatOrNull()
                                            ?: 0f).toInt()
                                    val id = data["id"]?.toString() ?: ""

                                    productsJSONArray.put("""{$bundlepromocode"goodstagid":$goodstagid,"id":"$id","num":0,$orderremark"operatetype":2,"selectstate":0,"_uuid":"${UUID.randomUUID()}"}""")
                                }
                            }

                            sellerJSONArray.put("""{"isFromCart":false,"products":$productsJSONArray,"sellerid":"$currentSellerid","shopid":"$currentShopid"}""")
                        }

                        val sellerStr =
                            sellerJSONArray.toString().replace("\\", "").replace("\"{", "{")
                                .replace("}\"", "}")

                        """{"addressid":"$addressid","isFromCart":false,"location":{"lat":"$lat","lng":"$lng","_uuid":"${UUID.randomUUID()}"},"pagesource":"","seller":$sellerStr,"source":1,"sourcePage":""}"""
                    }

                    else -> {
                        """"""
                    }
                }

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "cartClear: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 添加商品到购物车，目前只支持单商品添加
     * @param selectedAddress 选中的地址
     * @param inputString inputString
     * @param resultcode 结果码
     * @return 添加商品到购物车响应结果
     */
    suspend fun addCartGoods(
        inputString: String,
        selectedAddress: AddressItem,
        resultcode: String? = null
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "addCart: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                val commonParams = requestHelper.getAppCommonParams()

                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "addCart: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }

                //selectedAddress
                val addressid = selectedAddress.id
                val location = selectedAddress.location
                val lat = location?.lat
                val lng = location?.lng

                //对inputString进行匹配
                val operation = if (inputString.contains(";")) {
                    "multi"
                } else {
                    "one"
                }

                // 构建业务参数
                val businessParams = buildAppBusinessParams()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(RequestConfig.Path.ADD_CART_PATH, businessParams, commonParams)

                // 构建请求体
                val requestBody = when (operation) {
                    "one" -> {
                        val goodArr = inputString.split(",")
                        val id = goodArr[0]
                        val num = (goodArr[1].toInt() * 100).toString()
                        val goodstagid =
                            if (goodArr.size > 2 && goodArr[2].isNotEmpty() && goodArr[2] != "0") {
                                "\"goodstagid\":\"${goodArr[2]}\","
                            } else {
                                ""
                            }
                        val bundlepromocode =
                            if (goodArr.size > 3 && goodArr[3].isNotEmpty() && goodArr[3] != "NO-CODE") {
                                "\"bundlepromocode\":\"${goodArr[3]}\","
                            } else {
                                ""
                            }
                        val resultcodeStr =
                            if (resultcode != null) "\"resultcode\":\"$resultcode\"," else ""
                        """{"addressid":"$addressid","isFromCart":false,"location":{"lat":"$lat","lng":"$lng","_uuid":"${UUID.randomUUID()}"},"pagesource":"",$resultcodeStr"seller":[{"isFromCart":false,"products":[{$bundlepromocode$goodstagid$resultcodeStr"id":"$id","num":$num,"operatetype":1,"selectstate":1,"_uuid":"${UUID.randomUUID()}"}],"sellerid":"$sellerId","shopid":"$shopId"}],"source":0,"sourcePage":""}"""
                    }

                    "multi" -> {
                        """"""
                    }

                    else -> {
                        """"""
                    }

                }

                val needSignStr =
                    urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                        .replace("=", "")
                        .replace("&", "") + requestBody

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "addCart: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "addCart: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建Web业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                //selectedAddress
                val addressid = selectedAddress.id
                val location = selectedAddress.location
                val lat = location?.lat
                val lng = location?.lng

                //对inputString进行匹配
                val operation = if (inputString.contains(";")) {
                    "multi"
                } else {
                    "one"
                }

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.ADD_CART_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 构建请求体
                val requestBody = when (operation) {
                    "one" -> {
                        val goodArr = inputString.split(",")
                        val id = goodArr[0]
                        val num = (goodArr[1].toInt() * 100).toString()
                        val goodstagid =
                            if (goodArr.size > 2 && goodArr[2].isNotEmpty() && goodArr[2] != "0") {
                                "\"goodstagid\":\"${goodArr[2]}\","
                            } else {
                                ""
                            }
                        val bundlepromocode =
                            if (goodArr.size > 3 && goodArr[3].isNotEmpty() && goodArr[3] != "NO-CODE") {
                                "\"bundlepromocode\":\"${goodArr[3]}\","
                            } else {
                                ""
                            }
                        val resultcodeStr =
                            if (resultcode != null) "\"resultcode\":\"$resultcode\"," else ""
                        """{"addressid":"$addressid","isFromCart":false,"location":{"lat":"$lat","lng":"$lng","_uuid":"${UUID.randomUUID()}"},"pagesource":"",$resultcodeStr"seller":[{"isFromCart":false,"products":[{$bundlepromocode$goodstagid"id":"$id","num":$num,"operatetype":1,"selectstate":1,"_uuid":"${UUID.randomUUID()}"}],"sellerid":"$sellerId","shopid":"$shopId"}],"source":0,"sourcePage":""}"""
                    }

                    "multi" -> {
                        """"""
                    }

                    else -> {
                        """"""
                    }
                }

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "addCart: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }
}