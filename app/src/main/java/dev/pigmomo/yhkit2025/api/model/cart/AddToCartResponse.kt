package dev.pigmomo.yhkit2025.api.model.cart

/**
 * 添加购物车响应数据类
 */
data class AddToCartResponse(
    val code: Int = 0,
    val message: String = "",
    val data: AddToCartData? = null,
    val now: Long = 0
)

/**
 * 添加购物车数据类
 */
data class AddToCartData(
    val id: String = "",                           // 商品ID
    val title: String = "",                        // 商品标题
    val price: AddToCartPrice? = null,             // 价格信息
    val batchFlag: Int = 0,                        // 批次标志
    val qty: Int = 0,                              // 库存数量
    val skuTags: List<String> = emptyList(),       // SKU标签列表
    val expirationDate: Int = 0,                   // 过期天数
    val categoryId: String = "",                   // 分类ID
    val subtitle: String = "",                     // 副标题
    val listimgs: List<String> = emptyList(),      // 商品图片列表
    val isspu: Int = 0,                            // 是否SPU
    val batchcode: String = "",                    // 批次代码
    val batchitems: List<BatchItem> = emptyList()  // 批次项目列表
)

/**
 * 添加购物车价格信息
 */
data class AddToCartPrice(
    val value: Int = 0,                            // 当前价格(分)
    val flag: String = "",                         // 价格标志(如"promotion"表示促销价)
    val superprice: Int = 0                        // 超级价格(分)
)

/**
 * 批次项目数据类
 */
data class BatchItem(
    val desc: String = "",                         // 描述
    val defaultBatch: String = "",                 // 默认批次
    val batchlist: List<BatchDetail> = emptyList() // 批次详情列表
)

/**
 * 批次详情数据类
 */
data class BatchDetail(
    val name: String = "",                         // 商品名称
    val image: String = "",                        // 商品图片
    val price: Int = 0,                            // 价格(分)
    val qty: Int = 0,                              // 库存数量
    val flag: String = "",                         // 价格标志
    val skucode: String = "",                      // SKU代码
    val batchdescription: String = "",             // 批次描述
    val isDefaultBatch: Int = 0                    // 是否默认批次(1表示是)
)
