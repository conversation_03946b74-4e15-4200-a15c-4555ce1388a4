package dev.pigmomo.yhkit2025.api.service

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.SignType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 商品服务类
 * 提供商品相关的API调用方法
 */
class ProductService(requestHelper: RequestHelper) : BaseService(requestHelper) {

    private val tag = "ProductService"
    private val serviceType = requestHelper.getServiceType()

    /**
     * 获取商品详情
     * @param code 商品编码
     * @param pickSelf 是否自提，默认为0
     * @param isFood 是否为食品，默认为0
     * @return 商品详情响应
     */
    suspend fun getSkuDetail(
        code: String,
        pickSelf: Int = 0,
        isFood: Int = 0
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        val sellerId = requestHelper.getSellerId()
        val shopId = requestHelper.getShopId()
        if (sellerId.isEmpty() || shopId.isEmpty()) {
            Log.e(tag, "getSkuDetail: sellerId or shopId is empty")
            return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
        }

        when (serviceType) {
            "app" -> {
                val commonParams = requestHelper.getAppCommonParams()

                val xyhBizParams = requestHelper.getXyhBizParams()
                    .ifEmpty { RequestConfig.XYH_BIZ_PARAMS_ORIGIN_APP }

                // 构建业务参数
                val businessParams = buildAppBusinessParams().apply {
                    put("shopid", shopId)
                    put("code", code)
                    put("pickself", pickSelf)
                    put("isfood", isFood)
                    put("elderly", 0)

                    // 位置信息（如果提供）
                    put("lat", "")
                    put("lng", "")

                    // AB测试数据
                    put("abdata", "{\"sku_detail_new_people_850\":\"B\"}")
                }

                // 构建URL
                val urlWithParams = buildAppApiUrl(
                    RequestConfig.Path.SKU_DETAIL_PATH,
                    businessParams,
                    commonParams
                )

                val needSignStr = urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                    .replace("=", "")
                    .replace("&", "")

                val needSignStrFixed = needSignStr.replace(
                    "%7B%22sku_detail_new_people_850%22%3A%22B%22%7D",
                    "{\"sku_detail_new_people_850\":\"B\"}"
                )

                // 生成签名
                val sign = requestHelper.generateSign(needSignStrFixed, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "getSkuDetail: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val headers = buildAppStandardHeaders(xyhBizParams, false, false)

                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                val commonParams = requestHelper.getMiniProgramCommonParams()

                val xyhBizParams = requestHelper.getXyhBizParams()
                    .ifEmpty { RequestConfig.XYH_BIZ_PARAMS_ORIGIN_MINI_PROGRAM }

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams().apply {
                    put("shopid", shopId)
                    put("code", code)
                    put("pickself", pickSelf)
                    put("isfood", isFood)
                    put("elderly", 0)
                    put("memberid", requestHelper.getUid())
                    put("distinctId", requestHelper.getUid())

                    // 位置信息（如果提供）
                    put("lat", "")
                    put("lng", "")

                    // AB测试数据
                    put("abdata", "{\"sku_detail_new_people_850\":\"B\"}")
                }

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.SKU_DETAIL_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    "",
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }
}
