package dev.pigmomo.yhkit2025.api.model.product

/**
 * 促销店铺
 */
data class PromotionShop(
    val sellerid: Int?,
    val shopid: String?
)

/**
 * 已领取优惠券
 */
data class ReceiveCoupon(
    val catalog: Int?,
    val name: String?,
    val amount: Int?,
    val realm: String?,
    val sellers: List<String>?,
    val promotionShops: List<PromotionShop>?,
    val label: Int?,
    val couponTimeType: Int?,
    val loginType: String?,
    val activityShops: List<String>?,
    val orderLimits: List<Int>?,
    val activityStatus: String?,
    val activityStartTime: Long?,
    val activityEndTime: Long?,
    val showDescription: String?,
    val shopCouponSaleChanelLabel: Map<String, Int>?,
    val couponImageUrl: String?,
    val deliveryLimits: List<Int>?,
    val code: String?,
    val status: Int?,
    val startDate: String?,
    val endDate: String?,
    val expirationDesc: String?,
    val deliveredDate: String?,
    val ext: String?,
    val expirationDescColor: String?,
    val couponNotStartedDesc: String?,
    val expirationTile: String?,
    val expirationTileColor: String?,
    val details: List<String>?,
    val memberLevelTag: Map<String, Any>?,
    val promotioncode: String?,
    val desc: String?,
    val orderminamount: Int?,
    val minmemberlevel: Int?,
    val actionurl: String?,
    val realmid: Int?,
    val conditiondesc: String?,
    val shoprealm: String?,
    val descriptions: List<String>?,
    val scope: String?,
    val usescenario: Int?,
    val useprompt: String?,
    val iscurrentavailable: Int?,
    val salechannel: Long?,
    val taglist: List<TagItem>?,
    val newcustomer: Int?,
    val delaydays: Int?,
    val expirationdays: Int?,
    val availablefrom: Long?,
    val availableto: Long?,
    val couponlatitude: String?,
    val shopsellers: List<String>?,
    val combine: Int?,
    val desc2: String?,
    val date: String?,
    val expireddate: String?,
    val subdeliverytype: Int?,
    val receivedbefore: Int?,
    val couponCount: Int?,
    val isappmemberexclusivecoupon: Boolean?
)

/**
 * 标签项目
 */
data class TagItem(
    val type: String?,
    val text: String?,
    val imgurl: String?,
    val state: Long?,
    val sort: Long?,
    val benefit: Long?,
    val threshold: Long?,
    val style: Int?,
    val tagtype: String?
)

/**
 * 促销弹窗
 */
data class PromotionPopVo(
    val promotioncode: String?,
    val poptitle: String?,
    val popcontent: String?
)

/**
 * 营销信息
 */
data class MarketingInfo(
    val markets: List<Any>?
)

/**
 * 卖家信息
 */
data class SellerInfo(
    val id: String?,
    val title: String?,
    val icon: String?,
    val action: String?
)

/**
 * SKU状态信息
 */
data class SkuStatusInfo(
    val status: Int?,
    val statusDesc: String?,
    val arrivalTimeDesc: String?,
    val color: String?,
    val pattern: String?,
    val sellerservicedesc: String?,
    val deliverydesc: String?
)

/**
 * 二级卖家分类
 */
data class SecondSellerCategoryVo(
    val id: Long?,
    val name: String?
)

/**
 * 日期信息
 */
data class DateInfoVo(
    val expirationDate: Int?
)

/**
 * SKU批次弹窗描述
 */
data class SkuBatchPopDes(
    val poptitle: String?,
    val popcontent: String?
)

/**
 * SKU服务描述
 */
data class SkuServiceDes(
    val freightdetail: List<FreightDetail>?,
    val servicename: String?,
    val tagImg: String?
)

/**
 * 运费详情
 */
data class FreightDetail(
    val title: String?,
    val content: String?,
    val type: Int?,
    val icon: String?,
    val exclusion: Int?,
    val sort: Int?,
    val location: String?
)

/**
 * 属性信息
 */
data class AttributeInfo(
    val attributes: String?,
    val desc: String?,
    val remark: String?
)

/**
 * 属性弹窗
 */
data class AttributesPopup(
    val recommendattributes: List<AttributeInfo>?,
    val baseattributes: List<AttributeInfo>?
)

/**
 * 收藏按钮
 */
data class FavoriteButton(
    val state: Int?
)

/**
 * 弹幕信息
 */
data class BarrageInfo(
    val barrageInterval: Int?,
    val list: List<BarrageItem>?
)

/**
 * 弹幕项目
 */
data class BarrageItem(
    val text: String?,
    val imgUrl: String?
)

/**
 * 分类入口
 */
data class CategoryEntryVo(
    val categoryName: String?,
    val subCategoryName: String?,
    val actionUrl: String?,
    val imgUrl: String?
)

/**
 * 大促销活动
 */
data class BigSalesPromotionVo(
    val bigSales: Boolean?
)
