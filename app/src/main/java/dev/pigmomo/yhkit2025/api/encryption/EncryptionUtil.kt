package dev.pigmomo.yhkit2025.api.encryption

import android.util.Log
import java.security.KeyFactory
import java.security.MessageDigest
import java.security.spec.X509EncodedKeySpec
import java.util.Base64
import javax.crypto.Cipher

/**
 * 永辉特定的加密工具类
 * 提供永辉API所需的各种加密和签名方法
 */
object EncryptionUtil {
    private const val TAG = "EncryptionUtil"

    // 签名密钥
    private const val APPWEB_SIGN_KEY_FOR_ACTIVITY = "YONGHUI601933"
    private const val MINIPROGRAM_SIGN_KEY = "YH601933yCzc"
    private const val MINIPROGRAM_SALT = "EMHviKzks2E9WWiO"
    private const val MINIPROGRAMWEB_SIGN_KEY_FOR_ACTIVITY = "FcuhuxfY2vMSafr3"

    // RSA公钥 永辉卡信息
    private const val RSA_PUBLIC_KEY =
        "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALu8lMcv4PcO2xAvCHKyzP1Iab242Uk/5xJ0gULQRJ9s63KojjgsRy4cgOzvRWVub75jIKGUSBLZlWrmFnZ242MCAwEAAQ=="

    // RSA公钥 密码
    private const val RSA_PUBLIC_KEY_PASSWORD =
        "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALp4K+tLMpO09J0zPGq1N94KYGZ3wy3xNpH2QUv1OIwI3enrMtZzFw7zGqmWq+/6zNa3ZrNCI+lMpg8jYgUtpAECAwEAAQ=="

    /**
     * 计算字符串的MD5哈希值
     * @param input 输入字符串
     * @return MD5哈希值（16进制字符串）
     */
    fun md5(input: String): String {
        return try {
            val md = MessageDigest.getInstance("MD5")
            md.update(input.toByteArray())
            md.digest().joinToString("") { byte -> String.format("%02x", byte) }
        } catch (e: Exception) {
            Log.e(TAG, "MD5计算失败: ${e.message}", e)
            ""
        }
    }

    /**
     * 处理URL参数，提取并排序
     * @param url 原始URL字符串
     * @return 处理后的参数字符串
     */
    private fun processUrlParams(url: String): String {
        return if (url.contains("?")) {
            url.split("?")[1].split("&").sorted().joinToString("&")
                .replace("=", "").replace("&", "")
        } else {
            ""
        }
    }

    /**
     * appWeb签名
     * 用于永辉AppWeb活动签名
     * @param url 请求URL
     * @param body 请求体（默认为空）
     * @return 签名字符串
     */
    fun appWebSign(url: String, body: String = ""): String {
        val sortedUrl = processUrlParams(url)
        Log.d(TAG, "appWebSignForActivity sortedUrl: $sortedUrl")

        // 构建签名字符串
        val needSignString = "$APPWEB_SIGN_KEY_FOR_ACTIVITY$sortedUrl$body"
        Log.d(TAG, "appWebSignForActivity needSignString: $needSignString")

        // 计算MD5
        val sign = md5(needSignString)
        Log.d(TAG, "appWebSignForActivity sign: $sign")

        return sign
    }

    /**
     * 小程序签名
     * 用于永辉小程序API的签名生成
     * @param url 请求URL
     * @param body 请求体（JSON字符串，默认为空）
     * @param distinctId 用户标识ID（默认为空）
     * @return 包含签名的Map，可直接用于请求参数
     */
    fun miniProgramSign(url: String, body: String = "", distinctId: String = ""): String {
        Log.d(TAG, "miniprogramSign url: $url")
        Log.d(TAG, "miniprogramSign body: $body")

        // 从URL提取参数构建signConfig
        val signConfig = mutableMapOf<String, Any>()
        signConfig["signKey"] = MINIPROGRAM_SIGN_KEY

        if (url.contains("?")) {
            val queryString = url.split("?")[1]
            val queryParams = queryString.split("&")

            queryParams.forEach { param ->
                val pair = param.split("=")
                if (pair.size == 2) {
                    val paramName = pair[0]
                    val paramValue = java.net.URLDecoder.decode(pair[1], "UTF-8")

                    // 排除特定参数
                    if (paramName != "distinctId" && paramName != "timestamp" && paramName != "sign") {
                        signConfig[paramName] = paramValue
                    }
                }
            }
        }

        // 构建签名对象s
        val s = mutableMapOf<String, Any>()

        if (body.isNotEmpty()) {
            s["json"] = body
        }

        // 使用url中的timestamp
        s["timestamp"] =
            url.split("?")[1].split("&").find { it.startsWith("timestamp=") }?.split("=")[1] ?: ""
        Log.d(TAG, "miniProgramSign timestamp: ${s["timestamp"]}")

        if (distinctId.isNotEmpty()) {
            s["distinctId"] = distinctId
        } else if (signConfig.containsKey("openId")) {
            s["distinctId"] = signConfig["openId"].toString()
        }

        // 合并参数（排除signKey和json）
        val mergedParams = mutableMapOf<String, Any>()
        signConfig.forEach { (key, value) ->
            if (key != "signKey") {
                mergedParams[key] = value
            }
        }

        s.forEach { (key, value) ->
            if (key != "json") {
                mergedParams[key] = value
            }
        }

        // 构建签名字符串
        var signString = MINIPROGRAM_SALT

        // 按键名排序
        val sortedKeys = mergedParams.keys.sorted()

        // 拼接键值对
        sortedKeys.forEach { key ->
            val value = mergedParams[key]
            if (value != null) {
                signString += key + value.toString()
            }
        }

        // 添加json参数（如果有）
        val jsonValue = s["json"]
        if (jsonValue != null) {
            signString += jsonValue.toString()
        }

        Log.d(TAG, "生成签名字符串: $signString")

        // 计算MD5签名
        val sign = md5(signString)
        Log.d(TAG, "miniprogramSign 生成的签名: $sign")

        // 将签名添加到结果中
        mergedParams["sign"] = sign

        return sign
    }

    /**
     * 小程序WEB签名，永辉卡界面
     * 用于永辉小程序API的签名生成
     * @param url 请求URL
     * @param body 请求体（JSON字符串，默认为空）
     * @return 签名后的字符串
     */
    fun miniProgramWebSignForInfo(url: String, body: String = ""): String {
        val sortedUrl = processUrlParams(url)
        Log.d(TAG, "miniProgramSignForInfo sortedUrl: $sortedUrl")

        // 构建签名字符串
        val needSignString = "$MINIPROGRAM_SIGN_KEY$sortedUrl$body"
        Log.d(TAG, "miniProgramSignForInfo needSignString: $needSignString")

        // 计算MD5
        val sign = md5(needSignString)
        Log.d(TAG, "miniProgramSignForInfo sign: $sign")

        return sign
    }

    /**
     * 小程序WEB签名，助力券、积分签到
     * 用于永辉小程序API的签名生成
     * @param url 请求URL
     * @param body 请求体（默认为空）
     * @return 签名字符串
     */
    fun miniProgramWebSignForActivity(url: String, body: String = ""): String {
        val sortedUrl = processUrlParams(url)
        Log.d(TAG, "appWebSignForInfo sortedUrl: $sortedUrl")

        // 构建签名字符串
        val needSignString = "$MINIPROGRAMWEB_SIGN_KEY_FOR_ACTIVITY$sortedUrl$body"
        Log.d(TAG, "appWebSignForInfo needSignString: $needSignString")

        // 计算MD5
        val sign = md5(needSignString)
        Log.d(TAG, "appWebSignForInfo sign: $sign")

        return sign
    }

    //X-YH-Biz-Params:"lat=29.539315&lng=106.482004&addressId=60012736&appid=wxc9cf7c95499ee604&cityid=3&shopid=9162&sellerid=7&area="
    //X-YH-Context:origin=wechatmini&morse=1，1表示启用miniprogramXYHSign
    /**
     * 小程序XYH签名
     * 用于永辉小程序XYH签名
     * @param input 输入字符串
     * @return 签名后的字符串
     */
    fun miniProgramXYHSign(input: String): String {
        if (input.isEmpty()) return ""

        // 字符转换为ASCII码
        fun charCode(c: Char): Int = c.code

        // 检查字符是否是点号(.)
        fun isDot(c: Char): Boolean = charCode(c) == 46

        // 检查字符是否是小写字母
        fun isLowerCase(c: Char): Boolean = c.code in 97..122

        // 检查字符是否是大写字母
        fun isUpperCase(c: Char): Boolean = c.code in 65..90

        // 检查字符是否在指定范围内
        fun isInRange(c: Char, start: Int, end: Int): Boolean {
            val code = charCode(c)
            return code >= start && code <= end
        }

        // 数字字符转换
        fun convertDigit(c: Char): String {
            val digitMap = arrayOf("$", "-", "_", "+", "!", "*", "'", "(", ")", ",")
            return digitMap[c.code - 48]
        }

        // 循环调整字符码点
        fun adjustCharCode(code: Int, min: Int, max: Int): Int {
            return when {
                code < min -> max - min + code + 1
                code > max -> code - max + min - 1
                else -> code
            }
        }

        // 小写字母转换
        fun convertLowerCase(c: Char): Char {
            var code = charCode(c) - 5
            code = adjustCharCode(code, 97, 122)
            return code.toChar()
        }

        // 大写字母转换
        fun convertUpperCase(c: Char): Char {
            var code = charCode(c) + 4
            code = adjustCharCode(code, 65, 90)
            return code.toChar()
        }

        // 主转换逻辑
        val result = StringBuilder()
        for (c in input) {
            when {
                isInRange(c, 48, 57) -> result.append(convertDigit(c))
                isLowerCase(c) -> result.append(convertLowerCase(c))
                isUpperCase(c) -> result.append(convertUpperCase(c))
                isDot(c) -> result.append("0")
                else -> result.append(c)
            }
        }

        Log.d(TAG, "miniprogramXYHSign input: $input")
        Log.d(TAG, "miniprogramXYHSign result: $result")

        return result.toString()
    }

    /**
     * RSA加密
     * 使用永辉公钥进行RSA加密
     * @param input 待加密字符串
     * @return 加密后的Base64编码字符串，失败时返回空字符串
     */
    fun rsaEncrypt(input: String): String {
        return try {
            // 解码公钥
            val publicBytes = Base64.getDecoder().decode(RSA_PUBLIC_KEY)
            val keySpec = X509EncodedKeySpec(publicBytes)
            val keyFactory = KeyFactory.getInstance("RSA")
            val publicKey = keyFactory.generatePublic(keySpec)

            // 初始化加密器
            val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
            cipher.init(Cipher.ENCRYPT_MODE, publicKey)

            // 加密数据
            val encryptedBytes = cipher.doFinal(input.toByteArray())

            // 返回Base64编码的加密结果
            Base64.getEncoder().encodeToString(encryptedBytes)
        } catch (e: Exception) {
            Log.e(TAG, "RSA encryption failed: ${e.message}", e)
            ""
        }
    }

    /**
     * RSA加密（密码）
     * 使用永辉公钥进行RSA加密
     * @param input 待加密字符串
     * @return 加密后的Base64编码字符串，失败时返回空字符串
     */
    fun rsaEncryptPassword(input: String): String {
        return try {
            Log.d(TAG, "RSA encryption input: $input")

            // 解码公钥
            val publicBytes = Base64.getDecoder().decode(RSA_PUBLIC_KEY_PASSWORD)
            val keySpec = X509EncodedKeySpec(publicBytes)
            val keyFactory = KeyFactory.getInstance("RSA")
            val publicKey = keyFactory.generatePublic(keySpec)

            // 初始化加密器
            val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
            cipher.init(Cipher.ENCRYPT_MODE, publicKey)

            // 加密数据
            val encryptedBytes = cipher.doFinal(input.toByteArray())

            // 返回Base64编码的加密结果
            val result = Base64.getEncoder().encodeToString(encryptedBytes)

            Log.d(TAG, "RSA encryption result: $result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "RSA encryption failed: ${e.message}", e)
            ""
        }
    }
} 