package dev.pigmomo.yhkit2025.data.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import java.util.Date

/**
 * 到货监控实体类
 * 监控商品详情页的到货状态
 */
@Entity(
    tableName = "arrival_monitors",
    foreignKeys = [
        ForeignKey(
            entity = ProductEntity::class,
            parentColumns = ["product_id"],
            childColumns = ["product_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("product_id"), // 加快基于商品ID的查询
        Index("user_token"), // 加快基于用户令牌的查询
        Index("monitor_status"), // 加快基于监控状态的查询
        Index("availability_status"), // 加快基于可用性状态的查询
        Index("create_time"), // 加快基于创建时间的查询
        Index("last_check_time") // 加快基于最后检查时间的查询
    ]
)
data class ArrivalMonitorEntity(
    /**
     * 监控记录ID，主键
     */
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "monitor_id")
    val monitorId: Long = 0,
    
    /**
     * 商品ID，外键
     */
    @ColumnInfo(name = "product_id")
    val productId: String,
    
    /**
     * 用户令牌UID
     */
    @ColumnInfo(name = "user_token")
    val userToken: String,
    
    /**
     * 用户手机号
     */
    @ColumnInfo(name = "phone_number")
    val phoneNumber: String = "",
    
    /**
     * 监控状态 (ACTIVE, PAUSED, STOPPED)
     */
    @ColumnInfo(name = "monitor_status")
    val monitorStatus: String = "ACTIVE",
    
    /**
     * 可用性状态 (AVAILABLE, OUT_OF_STOCK, COMING_SOON, DISCONTINUED)
     */
    @ColumnInfo(name = "availability_status")
    val availabilityStatus: String = "OUT_OF_STOCK",
    
    /**
     * 上次检查时的可用性状态
     */
    @ColumnInfo(name = "last_availability_status")
    val lastAvailabilityStatus: String = "OUT_OF_STOCK",
    
    /**
     * 预计到货时间
     */
    @ColumnInfo(name = "estimated_arrival_time")
    val estimatedArrivalTime: Date? = null,
    
    /**
     * 实际到货时间
     */
    @ColumnInfo(name = "actual_arrival_time")
    val actualArrivalTime: Date? = null,
    
    /**
     * 缺货开始时间
     */
    @ColumnInfo(name = "out_of_stock_since")
    val outOfStockSince: Date? = null,
    
    /**
     * 缺货持续时间（小时）
     */
    @ColumnInfo(name = "out_of_stock_duration_hours")
    val outOfStockDurationHours: Long = 0,
    
    /**
     * 检查间隔（分钟）
     */
    @ColumnInfo(name = "check_interval")
    val checkInterval: Int = 15,
    
    /**
     * 是否启用通知
     */
    @ColumnInfo(name = "notification_enabled")
    val notificationEnabled: Boolean = true,
    
    /**
     * 通知方式 (PUSH, SMS, EMAIL)
     */
    @ColumnInfo(name = "notification_type")
    val notificationType: String = "PUSH",
    
    /**
     * 最后检查时间
     */
    @ColumnInfo(name = "last_check_time")
    val lastCheckTime: Date? = null,
    
    /**
     * 下次检查时间
     */
    @ColumnInfo(name = "next_check_time")
    val nextCheckTime: Date? = null,
    
    /**
     * 创建时间
     */
    @ColumnInfo(name = "create_time")
    val createTime: Date = Date(),
    
    /**
     * 最后更新时间
     */
    @ColumnInfo(name = "update_time")
    val updateTime: Date = Date(),
    
    /**
     * 监控优先级 (HIGH, MEDIUM, LOW)
     */
    @ColumnInfo(name = "priority")
    val priority: String = "MEDIUM",
    
    /**
     * 备注信息
     */
    @ColumnInfo(name = "notes")
    val notes: String = ""
)

/**
 * 可用性状态枚举
 */
enum class AvailabilityStatus {
    AVAILABLE,     // 有货
    OUT_OF_STOCK,  // 缺货
    COMING_SOON,   // 即将到货
    DISCONTINUED   // 停产
}
