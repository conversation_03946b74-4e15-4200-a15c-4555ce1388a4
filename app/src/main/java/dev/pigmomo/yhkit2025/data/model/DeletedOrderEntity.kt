package dev.pigmomo.yhkit2025.data.model

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 删除订单记录实体类
 * 用于保存所有被删除的订单信息，方便后续查询和统计
 */
@Entity(
    tableName = "deleted_orders",
    indices = [
        Index("uid"), // 加快基于账号UID的查询
        Index("deleteTimestamp") // 加快基于删除时间的查询
    ]
)
data class DeletedOrderEntity(
    @PrimaryKey
    val orderId: String, // 订单ID（主键）
    
    val uid: String, // 账号UID
    
    val phoneNumber: String = "", // 手机号（可选，用于更好的记录）
    
    val orderTitle: String = "", // 订单标题（可选，用于更好的记录）
    
    val orderStatus: Int = 0, // 订单状态（删除时的状态）
    
    val deleteTimestamp: Long = System.currentTimeMillis(), // 删除时间戳
    
    val deleteDate: String = "", // 删除日期（格式化字符串，如：2025-01-07）
    
    val extraNote: String = "" // 额外备注信息
)
