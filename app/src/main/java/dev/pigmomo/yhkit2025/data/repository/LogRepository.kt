package dev.pigmomo.yhkit2025.data.repository

import dev.pigmomo.yhkit2025.data.model.LogEntity
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 日志仓库接口
 * 定义日志数据的业务逻辑操作
 */
interface LogRepository {
    /**
     * 保存日志记录
     * @param tokenUid 令牌唯一标识符
     * @param message 日志消息
     * @param tag 日志标签
     * @param logLevel 日志级别
     * @param phoneNumber 关联的手机号码
     * @param timestamp 时间戳，如果为null则自动生成
     * @return 插入的日志ID
     */
    suspend fun saveLog(
        tokenUid: String,
        message: String,
        tag: String = "ProcessRecorder",
        logLevel: String = "INFO",
        phoneNumber: String = "",
        timestamp: Date? = null
    ): Long
    
    /**
     * 批量保存日志
     * @param logs 日志实体列表
     * @return 插入的日志ID列表
     */
    suspend fun saveLogs(logs: List<LogEntity>): List<Long>
    
    /**
     * 获取指定令牌的所有日志
     * @param tokenUid 令牌唯一标识符
     * @return 日志实体Flow
     */
    fun getLogsByToken(tokenUid: String): Flow<List<LogEntity>>
    
    /**
     * 获取时间范围内的日志
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 日志实体Flow
     */
    fun getLogsByTimeRange(startTime: Date, endTime: Date): Flow<List<LogEntity>>
    
    /**
     * 获取特定标签的日志
     * @param tag 日志标签
     * @return 日志实体Flow
     */
    fun getLogsByTag(tag: String): Flow<List<LogEntity>>
    
    /**
     * 获取指定日志级别的所有日志
     * @param logLevel 日志级别
     * @return 日志实体Flow
     */
    fun getLogsByLevel(logLevel: String): Flow<List<LogEntity>>
    
    /**
     * 搜索日志内容
     * @param keyword 关键字
     * @return 日志实体Flow
     */
    fun searchLogs(keyword: String): Flow<List<LogEntity>>
    
    /**
     * 删除指定令牌的日志
     * @param tokenUid 令牌唯一标识符
     * @return 删除的记录数
     */
    suspend fun deleteLogsByToken(tokenUid: String): Int
    
    /**
     * 删除指定时间之前的日志
     * @param timestamp 时间戳
     * @return 删除的记录数
     */
    suspend fun deleteLogsBefore(timestamp: Date): Int
    
    /**
     * 获取所有日志
     * @return 日志实体Flow
     */
    fun getAllLogs(): Flow<List<LogEntity>>
    
    /**
     * 清空所有日志
     * @return 删除的记录数
     */
    suspend fun clearAllLogs(): Int
    
    /**
     * 分页获取所有日志
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    suspend fun getLogsWithPagination(offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 分页获取指定级别的日志
     * @param logLevel 日志级别
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    suspend fun getLogsByLevelWithPagination(logLevel: String, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 分页获取指定令牌的日志
     * @param tokenUid 令牌唯一标识符
     * @param logLevel 日志级别 (可选)
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    suspend fun getLogsByTokenWithPagination(tokenUid: String, logLevel: String?, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 分页获取指定标签的日志
     * @param tag 日志标签
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    suspend fun getLogsByTagWithPagination(tag: String, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 分页搜索日志内容
     * @param keyword 关键字
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    suspend fun searchLogsWithPagination(keyword: String, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 按日志级别和关键词分页搜索日志
     * @param logLevel 日志级别
     * @param keyword 搜索关键字
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    suspend fun searchLogsByLevelWithPagination(logLevel: String, keyword: String, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 获取指定手机号的所有日志
     * @param phoneNumber 手机号码
     * @return 日志实体Flow
     */
    fun getLogsByPhoneNumber(phoneNumber: String): Flow<List<LogEntity>>
    
    /**
     * 删除指定手机号的日志
     * @param phoneNumber 手机号码
     * @return 删除的记录数
     */
    suspend fun deleteLogsByPhoneNumber(phoneNumber: String): Int
    
    /**
     * 分页获取指定手机号的日志
     * @param phoneNumber 手机号码
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    suspend fun getLogsByPhoneNumberWithPagination(phoneNumber: String, offset: Int, limit: Int): List<LogEntity>
    
    /**
     * 获取指定令牌的最新一条日志记录
     * @param tokenUid 令牌唯一标识符
     * @return 最新的日志实体，如果没有则返回null
     */
    suspend fun getLatestLogByToken(tokenUid: String): LogEntity?

    /**
     * 获取日志总数
     * @return 日志总数
     */
    suspend fun getLogCount(): Int
}

