package dev.pigmomo.yhkit2025.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import dev.pigmomo.yhkit2025.data.model.DeletedOrderEntity
import kotlinx.coroutines.flow.Flow

/**
 * 删除订单记录数据访问对象
 * 提供对删除订单记录的数据库操作方法
 */
@Dao
interface DeletedOrderDao {
    
    /**
     * 获取所有删除订单记录，按删除时间倒序排列
     */
    @Query("SELECT * FROM deleted_orders ORDER BY deleteTimestamp DESC")
    fun getAllDeletedOrders(): Flow<List<DeletedOrderEntity>>
    
    /**
     * 根据账号UID获取删除订单记录
     */
    @Query("SELECT * FROM deleted_orders WHERE uid = :uid ORDER BY deleteTimestamp DESC")
    fun getDeletedOrdersByUid(uid: String): Flow<List<DeletedOrderEntity>>
    
    /**
     * 根据订单ID获取删除记录
     */
    @Query("SELECT * FROM deleted_orders WHERE orderId = :orderId")
    suspend fun getDeletedOrderByOrderId(orderId: String): DeletedOrderEntity?
    
    /**
     * 根据手机号获取删除订单记录
     */
    @Query("SELECT * FROM deleted_orders WHERE phoneNumber = :phoneNumber ORDER BY deleteTimestamp DESC")
    fun getDeletedOrdersByPhoneNumber(phoneNumber: String): Flow<List<DeletedOrderEntity>>
    
    /**
     * 根据日期范围获取删除订单记录
     */
    @Query("SELECT * FROM deleted_orders WHERE deleteTimestamp BETWEEN :startTimestamp AND :endTimestamp ORDER BY deleteTimestamp DESC")
    fun getDeletedOrdersByDateRange(startTimestamp: Long, endTimestamp: Long): Flow<List<DeletedOrderEntity>>
    
    /**
     * 获取指定账号在指定日期范围内的删除订单记录
     */
    @Query("SELECT * FROM deleted_orders WHERE uid = :uid AND deleteTimestamp BETWEEN :startTimestamp AND :endTimestamp ORDER BY deleteTimestamp DESC")
    fun getDeletedOrdersByUidAndDateRange(uid: String, startTimestamp: Long, endTimestamp: Long): Flow<List<DeletedOrderEntity>>
    
    /**
     * 插入删除订单记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDeletedOrder(deletedOrder: DeletedOrderEntity): Long
    
    /**
     * 批量插入删除订单记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDeletedOrders(deletedOrders: List<DeletedOrderEntity>): List<Long>
    
    /**
     * 更新删除订单记录
     */
    @Update
    suspend fun updateDeletedOrder(deletedOrder: DeletedOrderEntity)
    
    /**
     * 删除指定的删除订单记录
     */
    @Delete
    suspend fun deleteDeletedOrder(deletedOrder: DeletedOrderEntity)
    
    /**
     * 根据订单ID删除删除订单记录（已废弃，请使用 deleteDeletedOrderByOrderId）
     */
    @Deprecated("Use deleteDeletedOrderByOrderId instead", ReplaceWith("deleteDeletedOrderByOrderId(orderId)"))
    @Query("DELETE FROM deleted_orders WHERE orderId = :orderId")
    suspend fun deleteDeletedOrderById(orderId: String)
    
    /**
     * 根据订单ID删除删除订单记录
     */
    @Query("DELETE FROM deleted_orders WHERE orderId = :orderId")
    suspend fun deleteDeletedOrderByOrderId(orderId: String)
    
    /**
     * 根据账号UID删除所有相关的删除订单记录
     */
    @Query("DELETE FROM deleted_orders WHERE uid = :uid")
    suspend fun deleteDeletedOrdersByUid(uid: String)
    
    /**
     * 删除指定时间之前的删除订单记录（用于清理旧数据）
     */
    @Query("DELETE FROM deleted_orders WHERE deleteTimestamp < :timestamp")
    suspend fun deleteDeletedOrdersBeforeTimestamp(timestamp: Long): Int
    
    /**
     * 清空所有删除订单记录
     */
    @Query("DELETE FROM deleted_orders")
    suspend fun deleteAllDeletedOrders()
    
    /**
     * 获取删除订单记录总数
     */
    @Query("SELECT COUNT(*) FROM deleted_orders")
    suspend fun getDeletedOrderCount(): Int
    
    /**
     * 获取指定账号的删除订单记录总数
     */
    @Query("SELECT COUNT(*) FROM deleted_orders WHERE uid = :uid")
    suspend fun getDeletedOrderCountByUid(uid: String): Int
    
    /**
     * 获取指定日期范围内的删除订单记录总数
     */
    @Query("SELECT COUNT(*) FROM deleted_orders WHERE deleteTimestamp BETWEEN :startTimestamp AND :endTimestamp")
    suspend fun getDeletedOrderCountByDateRange(startTimestamp: Long, endTimestamp: Long): Int
    
    /**
     * 检查订单是否已被删除
     */
    @Query("SELECT EXISTS(SELECT 1 FROM deleted_orders WHERE orderId = :orderId)")
    suspend fun isOrderDeleted(orderId: String): Boolean
}
