package dev.pigmomo.yhkit2025.data.repository

import dev.pigmomo.yhkit2025.data.dao.LogDao
import dev.pigmomo.yhkit2025.data.model.LogEntity
import kotlinx.coroutines.flow.Flow
import java.util.Date
import java.util.concurrent.atomic.AtomicLong

/**
 * 日志仓库实现类
 * 实现日志数据的业务逻辑
 * @param logDao 日志DAO接口
 */
class LogRepositoryImpl(
    private val logDao: LogDao
) : LogRepository {
    
    // 用于确保时间戳唯一性的计数器
    private val timestampCounter = AtomicLong(0)
    
    /**
     * 生成唯一的时间戳
     * 确保短时间内插入的多条日志有不同的时间戳且保持顺序
     * @return 唯一的时间戳
     */
    private fun generateUniqueTimestamp(): Date {
        val currentTime = System.currentTimeMillis()
        val counter = timestampCounter.getAndIncrement()
        // 如果计数器过大，重置为0
        if (counter > 999) {
            timestampCounter.set(0)
        }
        // 将计数器值加到毫秒时间戳上，确保唯一性
        // 使用Long类型，Date构造函数只接受Long类型参数
        return Date(currentTime + (counter % 1000))
    }

    /**
     * 保存单条日志记录
     * @param tokenUid 令牌唯一标识符
     * @param message 日志消息
     * @param tag 日志标签
     * @param logLevel 日志级别
     * @param phoneNumber 关联的手机号码
     * @param timestamp 时间戳，如果为null则自动生成
     * @return 插入的日志ID
     */
    override suspend fun saveLog(
        tokenUid: String,
        message: String,
        tag: String,
        logLevel: String,
        phoneNumber: String,
        timestamp: Date?
    ): Long {
        val logEntity = LogEntity(
            tokenUid = tokenUid,
            message = message,
            timestamp = timestamp ?: generateUniqueTimestamp(),
            tag = tag,
            logLevel = logLevel,
            phoneNumber = phoneNumber
        )
        return logDao.insertLog(logEntity)
    }

    /**
     * 批量保存日志记录
     * @param logs 日志实体列表
     * @return 插入的日志ID列表
     */
    override suspend fun saveLogs(logs: List<LogEntity>): List<Long> {
        // 为每条日志生成唯一时间戳
        val logsWithUniqueTimestamps = logs.map { log ->
            log.copy(timestamp = generateUniqueTimestamp())
        }
        return logDao.insertLogs(logsWithUniqueTimestamps)
    }

    /**
     * 获取指定令牌的所有日志
     * @param tokenUid 令牌唯一标识符
     * @return 日志实体Flow
     */
    override fun getLogsByToken(tokenUid: String): Flow<List<LogEntity>> {
        return logDao.getLogsByToken(tokenUid)
    }

    /**
     * 获取时间范围内的日志
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 日志实体Flow
     */
    override fun getLogsByTimeRange(startTime: Date, endTime: Date): Flow<List<LogEntity>> {
        return logDao.getLogsByTimeRange(startTime, endTime)
    }

    /**
     * 获取特定标签的日志
     * @param tag 日志标签
     * @return 日志实体Flow
     */
    override fun getLogsByTag(tag: String): Flow<List<LogEntity>> {
        return logDao.getLogsByTag(tag)
    }

    /**
     * 获取指定日志级别的所有日志
     * @param logLevel 日志级别
     * @return 日志实体Flow
     */
    override fun getLogsByLevel(logLevel: String): Flow<List<LogEntity>> {
        return logDao.getLogsByLevel(logLevel)
    }

    /**
     * 搜索日志内容
     * @param keyword 关键字
     * @return 日志实体Flow
     */
    override fun searchLogs(keyword: String): Flow<List<LogEntity>> {
        return logDao.searchLogs(keyword)
    }

    /**
     * 删除指定令牌的日志
     * @param tokenUid 令牌唯一标识符
     * @return 删除的记录数
     */
    override suspend fun deleteLogsByToken(tokenUid: String): Int {
        return logDao.deleteLogsByToken(tokenUid)
    }

    /**
     * 删除指定时间之前的日志
     * @param timestamp 时间戳
     * @return 删除的记录数
     */
    override suspend fun deleteLogsBefore(timestamp: Date): Int {
        return logDao.deleteLogsBefore(timestamp)
    }

    /**
     * 获取所有日志
     * @return 日志实体Flow
     */
    override fun getAllLogs(): Flow<List<LogEntity>> {
        return logDao.getAllLogs()
    }

    /**
     * 清空所有日志
     * @return 删除的记录数
     */
    override suspend fun clearAllLogs(): Int {
        return logDao.clearAllLogs()
    }

    /**
     * 分页获取所有日志
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    override suspend fun getLogsWithPagination(offset: Int, limit: Int): List<LogEntity> {
        return logDao.getLogsWithPagination(offset, limit)
    }

    /**
     * 分页获取指定级别的日志
     * @param logLevel 日志级别
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    override suspend fun getLogsByLevelWithPagination(logLevel: String, offset: Int, limit: Int): List<LogEntity> {
        return logDao.getLogsByLevelWithPagination(logLevel, offset, limit)
    }

    /**
     * 分页获取指定令牌的日志
     * @param tokenUid 令牌唯一标识符
     * @param logLevel 日志级别 (可选)
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    override suspend fun getLogsByTokenWithPagination(
        tokenUid: String,
        logLevel: String?,
        offset: Int,
        limit: Int
    ): List<LogEntity> {
        return if (logLevel.isNullOrEmpty() || logLevel == "全部") {
            logDao.getLogsByTokenWithPagination(tokenUid, offset, limit)
        } else {
            logDao.getLogsByTokenAndLevelWithPagination(tokenUid, logLevel, offset, limit)
        }
    }

    /**
     * 分页获取指定标签的日志
     * @param tag 日志标签
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    override suspend fun getLogsByTagWithPagination(tag: String, offset: Int, limit: Int): List<LogEntity> {
        return logDao.getLogsByTagWithPagination(tag, offset, limit)
    }

    /**
     * 分页搜索日志内容
     * @param keyword 关键字
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    override suspend fun searchLogsWithPagination(keyword: String, offset: Int, limit: Int): List<LogEntity> {
        return logDao.searchLogsWithPagination(keyword, offset, limit)
    }

    /**
     * 按日志级别和关键词分页搜索日志
     * @param logLevel 日志级别
     * @param keyword 搜索关键字
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    override suspend fun searchLogsByLevelWithPagination(logLevel: String, keyword: String, offset: Int, limit: Int): List<LogEntity> {
        return logDao.searchLogsByLevelWithPagination(logLevel, keyword, offset, limit)
    }

    /**
     * 获取指定令牌的最新一条日志记录
     * @param tokenUid 令牌唯一标识符
     * @return 最新的日志实体，如果没有则返回null
     */
    override suspend fun getLatestLogByToken(tokenUid: String): LogEntity? {
        return try {
            // 添加超时保护，防止数据库查询卡死
            kotlinx.coroutines.withTimeoutOrNull(3000) {
                logDao.getLatestLogByToken(tokenUid)
            }
        } catch (e: Exception) {
            // 记录错误但不抛出异常，返回null让调用方处理
            android.util.Log.e("LogRepositoryImpl", "Error getting latest log for token $tokenUid", e)
            null
        }
    }

    /**
     * 获取指定手机号的所有日志
     * @param phoneNumber 手机号码
     * @return 日志实体Flow
     */
    override fun getLogsByPhoneNumber(phoneNumber: String): Flow<List<LogEntity>> {
        return logDao.getLogsByPhoneNumber(phoneNumber)
    }

    /**
     * 删除指定手机号的日志
     * @param phoneNumber 手机号码
     * @return 删除的记录数
     */
    override suspend fun deleteLogsByPhoneNumber(phoneNumber: String): Int {
        return logDao.deleteLogsByPhoneNumber(phoneNumber)
    }

    /**
     * 分页获取指定手机号的日志
     * @param phoneNumber 手机号码
     * @param offset 起始位置
     * @param limit 数量限制
     * @return 日志实体列表
     */
    override suspend fun getLogsByPhoneNumberWithPagination(phoneNumber: String, offset: Int, limit: Int): List<LogEntity> {
        return logDao.getLogsByPhoneNumberWithPagination(phoneNumber, offset, limit)
    }

    /**
     * 获取日志总数
     * @return 日志总数
     */
    override suspend fun getLogCount(): Int {
        return try {
            logDao.getLogCount()
        } catch (e: Exception) {
            android.util.Log.e("LogRepositoryImpl", "Error getting log count", e)
            0
        }
    }
}