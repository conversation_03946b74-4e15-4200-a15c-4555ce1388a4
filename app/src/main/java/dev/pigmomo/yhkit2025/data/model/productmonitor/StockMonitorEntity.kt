package dev.pigmomo.yhkit2025.data.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import java.util.Date

/**
 * 库存监控实体类
 * 监控购物车中商品的库存变化
 */
@Entity(
    tableName = "stock_monitors",
    foreignKeys = [
        ForeignKey(
            entity = ProductEntity::class,
            parentColumns = ["product_id"],
            childColumns = ["product_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("product_id"), // 加快基于商品ID的查询
        Index("user_token"), // 加快基于用户令牌的查询
        Index("monitor_status"), // 加快基于监控状态的查询
        Index("create_time"), // 加快基于创建时间的查询
        Index("last_check_time") // 加快基于最后检查时间的查询
    ]
)
data class StockMonitorEntity(
    /**
     * 监控记录ID，主键
     */
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "monitor_id")
    val monitorId: Long = 0,
    
    /**
     * 商品ID，外键
     */
    @ColumnInfo(name = "product_id")
    val productId: String,
    
    /**
     * 用户令牌UID
     */
    @ColumnInfo(name = "user_token")
    val userToken: String,
    
    /**
     * 用户手机号
     */
    @ColumnInfo(name = "phone_number")
    val phoneNumber: String = "",
    
    /**
     * 监控状态 (ACTIVE, PAUSED, STOPPED)
     */
    @ColumnInfo(name = "monitor_status")
    val monitorStatus: String = "ACTIVE",
    
    /**
     * 目标库存数量（当库存大于等于此值时触发通知）
     */
    @ColumnInfo(name = "target_stock")
    val targetStock: Int = 1,
    
    /**
     * 当前库存数量
     */
    @ColumnInfo(name = "current_stock")
    val currentStock: Int = 0,
    
    /**
     * 上次检查时的库存数量
     */
    @ColumnInfo(name = "last_stock")
    val lastStock: Int = 0,
    
    /**
     * 检查间隔（分钟）
     */
    @ColumnInfo(name = "check_interval")
    val checkInterval: Int = 5,
    
    /**
     * 是否启用通知
     */
    @ColumnInfo(name = "notification_enabled")
    val notificationEnabled: Boolean = true,
    
    /**
     * 通知方式 (PUSH, SMS, EMAIL)
     */
    @ColumnInfo(name = "notification_type")
    val notificationType: String = "PUSH",
    
    /**
     * 最后检查时间
     */
    @ColumnInfo(name = "last_check_time")
    val lastCheckTime: Date? = null,
    
    /**
     * 下次检查时间
     */
    @ColumnInfo(name = "next_check_time")
    val nextCheckTime: Date? = null,
    
    /**
     * 创建时间
     */
    @ColumnInfo(name = "create_time")
    val createTime: Date = Date(),
    
    /**
     * 最后更新时间
     */
    @ColumnInfo(name = "update_time")
    val updateTime: Date = Date(),
    
    /**
     * 监控优先级 (HIGH, MEDIUM, LOW)
     */
    @ColumnInfo(name = "priority")
    val priority: String = "MEDIUM",
    
    /**
     * 备注信息
     */
    @ColumnInfo(name = "notes")
    val notes: String = ""
)

/**
 * 监控状态枚举
 */
enum class MonitorStatus {
    ACTIVE,  // 活跃监控
    PAUSED,  // 暂停监控
    STOPPED  // 停止监控
}

/**
 * 通知类型枚举
 */
enum class NotificationType {
    PUSH,  // 推送通知
    SMS,   // 短信通知
    EMAIL  // 邮件通知
}

/**
 * 优先级枚举
 */
enum class Priority {
    HIGH,   // 高优先级
    MEDIUM, // 中优先级
    LOW     // 低优先级
}
