package dev.pigmomo.yhkit2025.utils.pointexchange

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.utils.common.FileUtils
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write

/**
 * 积分组队TeamCode管理类
 *
 * 用于管理积分组队teamCode的保存、读取和解析，支持线程安全和基于时间的锁定管理
 * 参考GameCodeManager的实现模式
 */
object TeamCodeManager {
    private const val TAG = "TeamCodeManager"
    private const val TEAM_CODE_FILE = "teamCodeList.txt"

    // 锁定超时时间（毫秒）- 15秒
    private const val LOCK_TIMEOUT_MS = 15_000L

    // 清理任务执行间隔（毫秒）- 5秒
    private const val CLEANUP_INTERVAL_MS = 5_000L

    // 最大使用账号次数，初始创建时为 1
    private const val MAX_USAGE_COUNT = 5

    // 用于保护teamCode操作的读写锁
    private val teamLock = ReentrantReadWriteLock()

    // 记录正在使用中的teamCode及其锁定时间，支持基于时间的自动释放
    private val inUseTeamCodes = ConcurrentHashMap<String, Long>()

    // 定时清理任务执行器
    private val cleanupExecutor: ScheduledExecutorService = Executors.newSingleThreadScheduledExecutor { r ->
        Thread(r, "TeamCodeManager-Cleanup").apply {
            isDaemon = true
        }
    }

    init {
        // 启动定时清理任务
        startCleanupTask()
    }

    /**
     * TeamCode信息数据类
     * @property timestamp 时间戳
     * @property phoneNumber 手机号码
     * @property teamCode 团队代码
     * @property teamStatus 团队状态 (0=进行中，其他=已完成)
     * @property usageCount 使用次数
     */
    data class TeamCodeInfo(
        val timestamp: String,
        val phoneNumber: String,
        val teamCode: String,
        val teamStatus: Int = 0,
        val usageCount: Int = 0
    )

    /**
     * 锁定信息数据类
     * @property key 锁定键
     * @property lockTime 锁定时间
     * @property remainingTime 剩余时间
     */
    data class LockInfo(
        val key: String,
        val lockTime: Long,
        val remainingTime: Long
    )

    /**
     * 启动定时清理任务
     */
    private fun startCleanupTask() {
        cleanupExecutor.scheduleWithFixedDelay({
            try {
                cleanupExpiredLocks()
            } catch (e: Exception) {
                Log.e(TAG, "Error during cleanup task", e)
            }
        }, CLEANUP_INTERVAL_MS, CLEANUP_INTERVAL_MS, TimeUnit.MILLISECONDS)
    }

    /**
     * 清理过期的锁定
     */
    private fun cleanupExpiredLocks() {
        val currentTime = System.currentTimeMillis()
        val expiredKeys = mutableListOf<String>()

        inUseTeamCodes.forEach { (key, lockTime) ->
            if (currentTime - lockTime > LOCK_TIMEOUT_MS) {
                expiredKeys.add(key)
            }
        }

        if (expiredKeys.isNotEmpty()) {
            expiredKeys.forEach { key ->
                inUseTeamCodes.remove(key)
                Log.d(TAG, "Cleaned up expired team code lock: $key")
            }
            Log.d(TAG, "Cleaned up ${expiredKeys.size} expired team code locks")
        }
    }

    /**
     * 强制清理所有锁定（紧急恢复用）
     * @return 清理的锁定数量
     */
    fun forceCleanupAllLocks(): Int {
        val count = inUseTeamCodes.size
        inUseTeamCodes.clear()
        Log.w(TAG, "Force cleaned up all $count team code locks")
        return count
    }

    /**
     * 释放指定的teamCode锁定
     * @param teamCode 团队代码
     */
    fun releaseTeamCodeLock(teamCode: String) {
        val removed = inUseTeamCodes.remove(teamCode)
        if (removed != null) {
            Log.d(TAG, "Released team code lock: $teamCode")
        }
    }

    /**
     * 批量释放teamCode锁定
     * @param teamCodes 团队代码列表
     */
    fun releaseTeamCodeLocks(teamCodes: List<String>) {
        var releasedCount = 0
        teamCodes.forEach { teamCode ->
            if (inUseTeamCodes.remove(teamCode) != null) {
                releasedCount++
            }
        }
        if (releasedCount > 0) {
            Log.d(TAG, "Released $releasedCount team code locks")
        }
    }

    /**
     * 获取当前锁定状态信息（监控和调试功能）
     *
     * @return 锁定状态信息列表
     */
    fun getLockStatusInfo(): List<LockInfo> {
        val currentTime = System.currentTimeMillis()
        return inUseTeamCodes.map { (key, lockTime) ->
            val remainingTime = maxOf(0, LOCK_TIMEOUT_MS - (currentTime - lockTime))
            LockInfo(key, lockTime, remainingTime)
        }.sortedBy { it.remainingTime }
    }

    /**
     * 获取锁定统计信息
     *
     * @return Pair<总锁定数, 即将过期数(剩余时间<3秒)>
     */
    fun getLockStatistics(): Pair<Int, Int> {
        val currentTime = System.currentTimeMillis()
        val total = inUseTeamCodes.size
        val soonExpired = inUseTeamCodes.count { (_, lockTime) ->
            (currentTime - lockTime) > (LOCK_TIMEOUT_MS - 3000)
        }
        return Pair(total, soonExpired)
    }

    /**
     * 解析teamCode行数据
     * @param lines 文件行列表
     * @return TeamCode信息列表
     */
    private fun parseTeamCodeLines(lines: List<String>): List<TeamCodeInfo> {
        return lines.mapNotNull { line ->
            try {
                val parts = line.trim().split(",")
                if (parts.size >= 4) {
                    TeamCodeInfo(
                        timestamp = parts[0],
                        phoneNumber = parts[1],
                        teamCode = parts[2],
                        teamStatus = parts.getOrNull(3)?.toIntOrNull() ?: 0,
                        usageCount = parts.getOrNull(4)?.toIntOrNull() ?: 1
                    )
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.w(TAG, "Failed to parse team code line: $line", e)
                null
            }
        }
    }

    /**
     * 保存teamCode（线程安全）
     *
     * @param context 上下文
     * @param teamCode 团队代码
     * @param phoneNumber 手机号码
     * @param teamStatus 团队状态
     * @return 是否保存成功
     */
    fun saveTeamCode(context: Context, teamCode: String, phoneNumber: String, teamStatus: Int = 0): Boolean {
        // 使用写锁保护，确保多线程环境下的安全性
        return teamLock.write {
            val timestamp = FileUtils.getCurrentTimeStamp()
            val content = "$timestamp,$phoneNumber,$teamCode,$teamStatus,1"

            // FileUtils本身已经是线程安全的
            FileUtils.appendToFile(context, TEAM_CODE_FILE, content)
        }
    }

    /**
     * 批量保存teamCode（线程安全）
     *
     * @param context 上下文
     * @param teamCodeInfoList teamCode信息列表
     * @return 是否全部保存成功
     */
    fun saveTeamCodes(context: Context, teamCodeInfoList: List<TeamCodeInfo>): Boolean {
        if (teamCodeInfoList.isEmpty()) {
            return true
        }

        return teamLock.write {
            val sb = StringBuilder()
            for (info in teamCodeInfoList) {
                sb.append("${info.timestamp},${info.phoneNumber},${info.teamCode},${info.teamStatus},${info.usageCount}\n")
            }

            FileUtils.appendToFile(context, TEAM_CODE_FILE, sb.toString(), false)
        }
    }

    /**
     * 获取所有teamCode信息（线程安全）
     *
     * @param context 上下文
     * @return teamCode信息列表
     */
    fun getAllTeamCodes(context: Context): List<TeamCodeInfo> {
        return teamLock.read {
            val lines = FileUtils.readLines(context, TEAM_CODE_FILE)
            parseTeamCodeLines(lines)
        }
    }

    /**
     * 获取teamCode文件路径
     *
     * @param context 上下文
     * @return 文件路径
     */
    fun getTeamCodeFilePath(context: Context): String {
        return FileUtils.getFilePath(context, TEAM_CODE_FILE)
    }

    /**
     * 获取按teamCode分组的信息（线程安全）
     *
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码，可选
     * @return 按teamCode分组的映射
     */
    fun getTeamCodesGroupedByCode(
        context: Context,
        excludePhoneNumber: String? = null
    ): Map<String, List<TeamCodeInfo>> {
        return teamLock.read {
            // 获取所有teamCode
            var teamCodes = getAllTeamCodes(context)

            // 如果需要排除指定手机号，则进行过滤
            if (excludePhoneNumber != null) {
                teamCodes = teamCodes.filter { it.phoneNumber != excludePhoneNumber }
            }

            // 按teamCode分组
            teamCodes.groupBy { it.teamCode }
        }
    }

    /**
     * 清空所有teamCode（线程安全）
     *
     * @param context 上下文
     * @return 是否清空成功
     */
    fun clearAllTeamCodes(context: Context): Boolean {
        return teamLock.write {
            FileUtils.clearFile(context, TEAM_CODE_FILE)
        }
    }

    /**
     * 检查teamCode是否已存在（线程安全）
     *
     * @param context 上下文
     * @param teamCode 团队代码
     * @return 是否已存在
     */
    fun isTeamCodeExists(context: Context, teamCode: String): Boolean {
        return teamLock.read {
            getAllTeamCodes(context).any { it.teamCode == teamCode }
        }
    }

    /**
     * 原子操作：获取并标记一个可用的teamCode（线程安全）
     * 确保在多线程环境下每个teamCode只被一个线程使用，且使用次数不超过4次
     *
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码，可选
     * @return 可用的teamCode信息，如果没有可用teamCode则返回null
     */
    fun getAndMarkTeamCode(context: Context, excludePhoneNumber: String? = null): TeamCodeInfo? {
        try {
            // 首先使用读锁获取所有teamCode，减少写锁持有时间
            val allTeamCodes = teamLock.read {
                getAllTeamCodes(context)
            }

            // 如果需要排除指定手机号，则进行过滤
            var teamCodes = allTeamCodes
            if (excludePhoneNumber != null) {
                teamCodes = teamCodes.filter { it.phoneNumber != excludePhoneNumber }
            }

            // 只选择状态为0（进行中）且使用次数小于5的teamCode
            val availableTeamCodes = teamCodes.filter {
                it.teamStatus == 0 && it.usageCount < MAX_USAGE_COUNT
            }

            if (availableTeamCodes.isEmpty()) {
                return null
            }

            // 在读锁中检查可用的teamCode
            val availableCodesWithKeys = teamLock.read {
                availableTeamCodes.map {
                    val key = it.teamCode
                    Pair(it, key)
                }.filter { (_, key) -> !inUseTeamCodes.containsKey(key) }
            }

            if (availableCodesWithKeys.isEmpty()) {
                return null
            }

            // 随机获取一个teamCode
            val (selectedCode, selectedKey) = availableCodesWithKeys.randomOrNull() ?: return null

            // 只在需要修改共享状态时使用写锁，并且尽量减少持有时间
            val marked = teamLock.write {
                // 再次检查是否可用，因为在获取写锁的过程中状态可能已经改变
                if (!inUseTeamCodes.containsKey(selectedKey)) {
                    val currentTime = System.currentTimeMillis()
                    inUseTeamCodes[selectedKey] = currentTime
                    Log.d(TAG, "Marked team code as in-use: $selectedKey at $currentTime")
                    true
                } else {
                    false
                }
            }

            return if (marked) selectedCode else null

        } catch (e: Exception) {
            Log.e(TAG, "Error getting and marking team code", e)
            return null
        }
    }

    /**
     * 删除指定的teamCode，并从使用中标记中移除（线程安全）
     *
     * @param context 上下文
     * @param teamCode 团队代码
     * @return 是否删除成功
     */
    fun deleteTeamCode(context: Context, teamCode: String): Boolean {
        return teamLock.write {
            // 从使用中标记中移除
            inUseTeamCodes.remove(teamCode)

            val allCodes = getAllTeamCodes(context)
            val filteredCodes = allCodes.filter { it.teamCode != teamCode }

            if (filteredCodes.size == allCodes.size) {
                // 没有找到要删除的teamCode
                return@write false
            }

            // 清空文件并重写所有保留的teamCode
            FileUtils.clearFile(context, TEAM_CODE_FILE)

            if (filteredCodes.isEmpty()) {
                return@write true
            }

            val sb = StringBuilder()
            for (code in filteredCodes) {
                sb.append("${code.timestamp},${code.phoneNumber},${code.teamCode},${code.teamStatus},${code.usageCount}\n")
            }

            FileUtils.writeToFile(context, TEAM_CODE_FILE, sb.toString())
        }
    }

    /**
     * 批量删除指定的teamCode（线程安全）
     *
     * @param context 上下文
     * @param teamCodeInfoList 要删除的teamCode信息列表
     * @return 成功删除的teamCode数量
     */
    fun deleteTeamCodes(context: Context, teamCodeInfoList: List<TeamCodeInfo>): Int {
        if (teamCodeInfoList.isEmpty()) {
            return 0
        }

        return teamLock.write {
            val allCodes = getAllTeamCodes(context)
            var deletedCount = 0

            // 创建一个集合，用于快速查找要删除的teamCode
            val toDeleteSet = teamCodeInfoList.map { it.teamCode }.toSet()

            // 过滤出要保留的teamCode
            val filteredCodes = allCodes.filter {
                val shouldDelete = it.teamCode in toDeleteSet
                if (shouldDelete) {
                    deletedCount++
                    // 从使用中标记中移除
                    inUseTeamCodes.remove(it.teamCode)
                }
                !shouldDelete
            }

            // 如果没有要删除的teamCode，直接返回
            if (deletedCount == 0) {
                return@write 0
            }

            // 清空文件并重写所有保留的teamCode
            FileUtils.clearFile(context, TEAM_CODE_FILE)

            if (filteredCodes.isNotEmpty()) {
                val sb = StringBuilder()
                for (code in filteredCodes) {
                    sb.append("${code.timestamp},${code.phoneNumber},${code.teamCode},${code.teamStatus},${code.usageCount}\n")
                }

                FileUtils.writeToFile(context, TEAM_CODE_FILE, sb.toString())
            }

            deletedCount
        }
    }

    /**
     * 更新teamCode的状态（线程安全）
     *
     * @param context 上下文
     * @param teamCode 团队代码
     * @param newStatus 新状态
     * @return 是否更新成功
     */
    fun updateTeamCodeStatus(context: Context, teamCode: String, newStatus: Int): Boolean {
        return teamLock.write {
            val allCodes = getAllTeamCodes(context).toMutableList()
            var updated = false

            for (i in allCodes.indices) {
                if (allCodes[i].teamCode == teamCode) {
                    allCodes[i] = allCodes[i].copy(teamStatus = newStatus)
                    updated = true
                }
            }

            if (!updated) {
                return@write false
            }

            // 重写文件
            FileUtils.clearFile(context, TEAM_CODE_FILE)

            if (allCodes.isNotEmpty()) {
                val sb = StringBuilder()
                for (code in allCodes) {
                    sb.append("${code.timestamp},${code.phoneNumber},${code.teamCode},${code.teamStatus},${code.usageCount}\n")
                }

                FileUtils.writeToFile(context, TEAM_CODE_FILE, sb.toString())
            }

            true
        }
    }

    /**
     * 增加teamCode的使用次数（线程安全）
     *
     * @param context 上下文
     * @param teamCode 团队代码
     * @return 是否更新成功
     */
    fun incrementTeamCodeUsage(context: Context, teamCode: String): Boolean {
        return teamLock.write {
            val allCodes = getAllTeamCodes(context).toMutableList()
            var updated = false

            for (i in allCodes.indices) {
                if (allCodes[i].teamCode == teamCode) {
                    allCodes[i] = allCodes[i].copy(usageCount = allCodes[i].usageCount + 1)
                    updated = true
                    break
                }
            }

            if (!updated) {
                return@write false
            }

            // 重写文件
            FileUtils.clearFile(context, TEAM_CODE_FILE)

            if (allCodes.isNotEmpty()) {
                val sb = StringBuilder()
                for (code in allCodes) {
                    sb.append("${code.timestamp},${code.phoneNumber},${code.teamCode},${code.teamStatus},${code.usageCount}\n")
                }

                FileUtils.writeToFile(context, TEAM_CODE_FILE, sb.toString())
            }

            true
        }
    }
}