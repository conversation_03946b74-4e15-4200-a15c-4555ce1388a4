package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.utils.common.FileUtils
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write

/**
 * 邀请码管理类
 *
 * 用于管理邀请有礼邀请码的保存、读取和解析，支持线程安全和基于时间的锁定管理
 * 参考GameCodeManager的实现模式
 */
object InvitationCodeManager {
    private const val TAG = "InvitationCodeManager"
    private const val INVITATION_CODE_FILE = "invitationCodeList.txt"

    // 锁定超时时间（毫秒）- 15秒
    private const val LOCK_TIMEOUT_MS = 15_000L

    // 清理任务执行间隔（毫秒）- 5秒
    private const val CLEANUP_INTERVAL_MS = 5_000L

    // 用于保护邀请码操作的读写锁
    private val invitationLock = ReentrantReadWriteLock()

    // 记录正在使用中的邀请码及其锁定时间，支持基于时间的自动释放
    private val inUseInvitationCodes = ConcurrentHashMap<String, Long>()

    // 定时清理任务执行器
    private val cleanupExecutor: ScheduledExecutorService = Executors.newSingleThreadScheduledExecutor { r ->
        Thread(r, "InvitationCodeManager-Cleanup").apply {
            isDaemon = true
        }
    }

    // 用于文件删除操作的互斥锁
    private val fileMutex = Mutex()

    init {
        // 启动定时清理任务
        startCleanupTask()
    }

    /**
     * 邀请码信息数据类
     * @property timestamp 时间戳
     * @property phoneNumber 手机号码
     * @property inviterID 邀请人ID
     * @property activityCode 活动代码
     * @property shopId 店铺ID
     */
    data class InvitationCodeInfo(
        val timestamp: String,
        val phoneNumber: String,
        val inviterID: String,
        val activityCode: String,
        val shopId: String
    ) {
        /**
         * 获取完整的邀请码字符串
         */
        fun getInvitationCodeString(): String {
            return "$inviterID,$activityCode,$shopId"
        }
    }

    /**
     * 启动定时清理任务
     */
    private fun startCleanupTask() {
        cleanupExecutor.scheduleWithFixedDelay({
            try {
                cleanupExpiredLocks()
            } catch (e: Exception) {
                Log.e(TAG, "Error during cleanup task", e)
            }
        }, CLEANUP_INTERVAL_MS, CLEANUP_INTERVAL_MS, TimeUnit.MILLISECONDS)
    }

    /**
     * 清理过期的锁定
     */
    private fun cleanupExpiredLocks() {
        val currentTime = System.currentTimeMillis()
        val expiredKeys = mutableListOf<String>()

        // 找出过期的锁定
        for ((key, lockTime) in inUseInvitationCodes) {
            if (currentTime - lockTime > LOCK_TIMEOUT_MS) {
                expiredKeys.add(key)
            }
        }

        // 移除过期的锁定
        for (key in expiredKeys) {
            inUseInvitationCodes.remove(key)
            Log.d(TAG, "Cleaned up expired invitation code lock: $key")
        }
    }

    /**
     * 手动释放邀请码锁定
     * @param invitationCodeString 邀请码字符串
     */
    fun releaseInvitationCodeLock(invitationCodeString: String) {
        inUseInvitationCodes.remove(invitationCodeString)
        Log.d(TAG, "Released invitation code lock: $invitationCodeString")
    }

    /**
     * 保存邀请码（线程安全）
     * 
     * @param context 上下文
     * @param inviterID 邀请人ID
     * @param activityCode 活动代码
     * @param shopId 店铺ID
     * @param phoneNumber 手机号码
     * @return 是否保存成功
     */
    fun saveInvitationCode(
        context: Context,
        inviterID: String,
        activityCode: String,
        shopId: String,
        phoneNumber: String
    ): Boolean {
        // 使用写锁保护，确保多线程环境下的安全性
        return invitationLock.write {
            val timestamp = FileUtils.getCurrentTimeStamp()
            val content = "$timestamp,$phoneNumber,$inviterID,$activityCode,$shopId"
            
            // FileUtils本身已经是线程安全的
            FileUtils.appendToFile(context, INVITATION_CODE_FILE, content)
        }
    }

    /**
     * 批量保存邀请码（线程安全）
     * 
     * @param context 上下文
     * @param invitationCodeInfoList 邀请码信息列表
     * @return 是否全部保存成功
     */
    fun saveInvitationCodes(context: Context, invitationCodeInfoList: List<InvitationCodeInfo>): Boolean {
        if (invitationCodeInfoList.isEmpty()) {
            return true
        }
        
        return invitationLock.write {
            val sb = StringBuilder()
            for (info in invitationCodeInfoList) {
                sb.append("${info.timestamp},${info.phoneNumber},${info.inviterID},${info.activityCode},${info.shopId}\n")
            }
            
            FileUtils.appendToFile(context, INVITATION_CODE_FILE, sb.toString(), false)
        }
    }

    /**
     * 获取所有邀请码信息（线程安全）
     * 
     * @param context 上下文
     * @return 邀请码信息列表
     */
    fun getAllInvitationCodes(context: Context): List<InvitationCodeInfo> {
        return invitationLock.read {
            val lines = FileUtils.readLines(context, INVITATION_CODE_FILE)
            parseInvitationCodeLines(lines)
        }
    }

    /**
     * 解析邀请码行数据
     * 
     * @param lines 文件行列表
     * @return 邀请码信息列表
     */
    private fun parseInvitationCodeLines(lines: List<String>): List<InvitationCodeInfo> {
        val invitationCodes = mutableListOf<InvitationCodeInfo>()
        
        for (line in lines) {
            if (line.isBlank()) continue
            
            try {
                val parts = line.split(",")
                if (parts.size >= 5) {
                    val timestamp = parts[0]
                    val phoneNumber = parts[1]
                    val inviterID = parts[2]
                    val activityCode = parts[3]
                    val shopId = parts[4]
                    
                    invitationCodes.add(
                        InvitationCodeInfo(
                            timestamp = timestamp,
                            phoneNumber = phoneNumber,
                            inviterID = inviterID,
                            activityCode = activityCode,
                            shopId = shopId
                        )
                    )
                }
            } catch (e: Exception) {
                Log.w(TAG, "Failed to parse invitation code line: $line", e)
            }
        }
        
        return invitationCodes
    }

    /**
     * 原子操作：获取并标记一个可用的邀请码（线程安全）
     * 确保在多线程环境下每个邀请码只被一个线程使用
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码，可选
     * @return 可用的邀请码信息，如果没有可用邀请码则返回null
     */
    fun getAndMarkInvitationCode(context: Context, excludePhoneNumber: String? = null): InvitationCodeInfo? {
        try {
            // 首先使用读锁获取所有邀请码，减少写锁持有时间
            val allInvitationCodes = invitationLock.read {
                getAllInvitationCodes(context)
            }
            
            // 如果需要排除指定手机号，则进行过滤
            var invitationCodes = allInvitationCodes
            if (excludePhoneNumber != null) {
                invitationCodes = invitationCodes.filter { it.phoneNumber != excludePhoneNumber }
            }
            
            // 在读锁中检查可用的邀请码
            val availableCodesWithKeys = invitationLock.read {
                invitationCodes.map { 
                    val key = it.getInvitationCodeString()
                    Pair(it, key) 
                }.filter { (_, key) -> !inUseInvitationCodes.containsKey(key) }
            }
            
            if (availableCodesWithKeys.isEmpty()) {
                return null
            }
            
            // 获取最新的一个邀请码
            val (selectedCode, selectedKey) = availableCodesWithKeys.maxByOrNull { (code, _) -> code.timestamp } ?: return null
            
            // 只在需要修改共享状态时使用写锁，并且尽量减少持有时间
            val marked = invitationLock.write {
                // 再次检查是否可用，因为在获取写锁的过程中状态可能已经改变
                if (!inUseInvitationCodes.containsKey(selectedKey)) {
                    val currentTime = System.currentTimeMillis()
                    inUseInvitationCodes[selectedKey] = currentTime
                    Log.d(TAG, "Marked invitation code as in-use: $selectedKey at $currentTime")
                    true
                } else {
                    false
                }
            }
            
            return if (marked) selectedCode else null
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting and marking invitation code", e)
            return null
        }
    }

    /**
     * 删除已使用的邀请码（线程安全）
     * 邀请码绑定成功后应该被删除，确保一次性使用
     * 
     * @param context 上下文
     * @param invitationCodeInfo 要删除的邀请码信息
     * @return 是否删除成功
     */
    suspend fun removeUsedInvitationCode(context: Context, invitationCodeInfo: InvitationCodeInfo): Boolean {
        return fileMutex.withLock {
            try {
                // 获取所有邀请码
                val allCodes = getAllInvitationCodes(context)
                
                // 过滤掉要删除的邀请码
                val remainingCodes = allCodes.filter { 
                    !(it.inviterID == invitationCodeInfo.inviterID && 
                      it.activityCode == invitationCodeInfo.activityCode && 
                      it.shopId == invitationCodeInfo.shopId)
                }
                
                // 重写文件
                val success = invitationLock.write {
                    // 清空文件
                    FileUtils.clearFile(context, INVITATION_CODE_FILE)
                    
                    // 写入剩余的邀请码
                    if (remainingCodes.isNotEmpty()) {
                        val sb = StringBuilder()
                        for (info in remainingCodes) {
                            sb.append("${info.timestamp},${info.phoneNumber},${info.inviterID},${info.activityCode},${info.shopId}\n")
                        }
                        FileUtils.appendToFile(context, INVITATION_CODE_FILE, sb.toString(), false)
                    } else {
                        true // 如果没有剩余邀请码，清空文件就算成功
                    }
                }
                
                if (success) {
                    Log.d(TAG, "Successfully removed used invitation code: ${invitationCodeInfo.getInvitationCodeString()}")
                    // 同时释放锁定
                    releaseInvitationCodeLock(invitationCodeInfo.getInvitationCodeString())
                }
                
                success
            } catch (e: Exception) {
                Log.e(TAG, "Error removing used invitation code", e)
                false
            }
        }
    }

    /**
     * 获取邀请码统计信息
     * 
     * @param context 上下文
     * @return 统计信息字符串
     */
    fun getInvitationCodeStats(context: Context): String {
        return invitationLock.read {
            val allCodes = getAllInvitationCodes(context)
            val inUseCount = inUseInvitationCodes.size
            "总邀请码: ${allCodes.size}, 使用中: $inUseCount, 可用: ${allCodes.size - inUseCount}"
        }
    }
}
