package dev.pigmomo.yhkit2025.utils.common

import android.content.Context
import android.os.Environment
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write

/**
 * 文件操作工具类
 *
 * 提供通用的文件读写、追加、清空等操作，支持线程安全
 */
object FileUtils {
    private const val TAG = "FileUtils"

    // 文件锁映射表，用于对每个文件进行独立的锁控制
    private val fileLocks = mutableMapOf<String, ReentrantReadWriteLock>()

    // 用于保护fileLocks映射表本身的锁
    private val mapLock = ReentrantReadWriteLock()

    /**
     * 获取指定文件的读写锁
     *
     * @param fileName 文件名
     * @return 对应文件的读写锁
     */
    private fun getLockForFile(fileName: String): ReentrantReadWriteLock {
        return mapLock.read {
            if (fileLocks.containsKey(fileName)) {
                fileLocks[fileName]!!
            } else {
                mapLock.write {
                    // 双重检查，避免在获取写锁期间其他线程已创建
                    fileLocks.getOrPut(fileName) { ReentrantReadWriteLock() }
                }
            }
        }
    }

    /**
     * 将内容追加到文件（线程安全）
     *
     * @param context 上下文
     * @param fileName 文件名
     * @param content 要追加的内容
     * @param addNewLine 是否在内容后添加换行符，默认为true
     * @return 是否追加成功
     */
    fun appendToFile(context: Context, fileName: String, content: String, addNewLine: Boolean = true): Boolean {
        val lock = getLockForFile(fileName)

        // 使用写锁保护文件写入操作
        return lock.write {
            try {
                val file = getOrCreateFile(context, fileName)
                val contentWithNewLine = if (addNewLine && !content.endsWith("\n")) "$content\n" else content

                FileOutputStream(file, true).use { fos ->
                    fos.write(contentWithNewLine.toByteArray())
                    fos.flush()
                }

                Log.d(TAG, "Content appended to file: $fileName")
                true
            } catch (e: IOException) {
                Log.e(TAG, "Failed to append content to file: $fileName", e)
                false
            }
        }
    }

    /**
     * 将内容写入文件（覆盖原有内容，线程安全）
     *
     * @param context 上下文
     * @param fileName 文件名
     * @param content 要写入的内容
     * @return 是否写入成功
     */
    fun writeToFile(context: Context, fileName: String, content: String): Boolean {
        val lock = getLockForFile(fileName)

        // 使用写锁保护文件写入操作
        return lock.write {
            try {
                val file = getOrCreateFile(context, fileName)

                FileOutputStream(file, false).use { fos ->
                    fos.write(content.toByteArray())
                    fos.flush()
                }

                Log.d(TAG, "Content written to file: $fileName")
                true
            } catch (e: IOException) {
                Log.e(TAG, "Failed to write content to file: $fileName", e)
                false
            }
        }
    }

    /**
     * 读取文件内容（线程安全）
     *
     * @param context 上下文
     * @param fileName 文件名
     * @return 文件内容列表，每行一条记录；如果文件不存在或读取失败，返回空列表
     */
    fun readLines(context: Context, fileName: String): List<String> {
        val lock = getLockForFile(fileName)

        // 使用读锁保护文件读取操作
        return lock.read {
            val file = getFile(context, fileName)
            if (!file.exists()) {
                return@read emptyList()
            }

            try {
                file.readLines()
            } catch (e: IOException) {
                Log.e(TAG, "Failed to read file: $fileName", e)
                emptyList()
            }
        }
    }

    /**
     * 读取文件内容为单个字符串（线程安全）
     *
     * @param context 上下文
     * @param fileName 文件名
     * @return 文件内容字符串；如果文件不存在或读取失败，返回空字符串
     */
    fun readText(context: Context, fileName: String): String {
        val lock = getLockForFile(fileName)

        // 使用读锁保护文件读取操作
        return lock.read {
            val file = getFile(context, fileName)
            if (!file.exists()) {
                return@read ""
            }

            try {
                file.readText()
            } catch (e: IOException) {
                Log.e(TAG, "Failed to read file: $fileName", e)
                ""
            }
        }
    }

    /**
     * 清空文件内容（线程安全）
     *
     * @param context 上下文
     * @param fileName 文件名
     * @return 是否清空成功
     */
    fun clearFile(context: Context, fileName: String): Boolean {
        val lock = getLockForFile(fileName)

        // 使用写锁保护文件清空操作
        return lock.write {
            val file = getFile(context, fileName)
            if (!file.exists()) {
                return@write true
            }

            try {
                FileOutputStream(file, false).close()
                Log.d(TAG, "File cleared: $fileName")
                true
            } catch (e: IOException) {
                Log.e(TAG, "Failed to clear file: $fileName", e)
                false
            }
        }
    }

    /**
     * 删除文件（线程安全）
     *
     * @param context 上下文
     * @param fileName 文件名
     * @return 是否删除成功
     */
    fun deleteFile(context: Context, fileName: String): Boolean {
        val lock = getLockForFile(fileName)

        // 使用写锁保护文件删除操作
        return lock.write {
            val file = getFile(context, fileName)
            if (!file.exists()) {
                return@write true
            }

            try {
                val result = file.delete()
                if (result) {
                    // 文件删除成功，从映射表中移除对应的锁
                    mapLock.write {
                        fileLocks.remove(fileName)
                    }
                    Log.d(TAG, "File deleted: $fileName")
                } else {
                    Log.e(TAG, "Failed to delete file: $fileName")
                }
                result
            } catch (e: Exception) {
                Log.e(TAG, "Error deleting file: $fileName", e)
                false
            }
        }
    }

    /**
     * 检查文件是否存在（线程安全）
     *
     * @param context 上下文
     * @param fileName 文件名
     * @return 文件是否存在
     */
    fun fileExists(context: Context, fileName: String): Boolean {
        val lock = getLockForFile(fileName)

        // 使用读锁保护文件检查操作
        return lock.read {
            getFile(context, fileName).exists()
        }
    }

    /**
     * 获取文件大小（字节数，线程安全）
     *
     * @param context 上下文
     * @param fileName 文件名
     * @return 文件大小（字节）；如果文件不存在，返回0
     */
    fun getFileSize(context: Context, fileName: String): Long {
        val lock = getLockForFile(fileName)

        // 使用读锁保护文件大小获取操作
        return lock.read {
            val file = getFile(context, fileName)
            if (file.exists()) file.length() else 0
        }
    }

    /**
     * 获取文件路径
     *
     * @param context 上下文
     * @param fileName 文件名
     * @return 文件的绝对路径
     */
    fun getFilePath(context: Context, fileName: String): String {
        return getFile(context, fileName).absolutePath
    }

    /**
     * 获取文件对象
     *
     * @param context 上下文
     * @param fileName 文件名
     * @return 文件对象
     */
    private fun getFile(context: Context, fileName: String): File {
        val dir = context.getExternalFilesDir(null) ?: context.filesDir
        return File(dir, fileName)
    }

    /**
     * 获取或创建文件（线程安全）
     *
     * @param context 上下文
     * @param fileName 文件名
     * @return 文件对象
     */
    private fun getOrCreateFile(context: Context, fileName: String): File {
        val file = getFile(context, fileName)

        if (!file.exists()) {
            try {
                file.createNewFile()
            } catch (e: IOException) {
                Log.e(TAG, "Failed to create file: $fileName", e)
            }
        }

        return file
    }

    /**
     * 获取当前时间戳字符串
     *
     * @param pattern 时间格式模式，默认为"yyyy-MM-dd HH:mm:ss"
     * @return 格式化的时间戳字符串
     */
    fun getCurrentTimeStamp(pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
        return SimpleDateFormat(pattern, Locale.getDefault()).format(Date())
    }

    /**
     * 原子操作：如果文件不存在则创建并写入内容（线程安全）
     *
     * @param context 上下文
     * @param fileName 文件名
     * @param content 要写入的内容
     * @return 是否操作成功
     */
    fun createFileIfNotExists(context: Context, fileName: String, content: String): Boolean {
        val lock = getLockForFile(fileName)

        return lock.write {
            val file = getFile(context, fileName)
            if (!file.exists()) {
                try {
                    file.createNewFile()
                    FileOutputStream(file).use { fos ->
                        fos.write(content.toByteArray())
                        fos.flush()
                    }
                    Log.d(TAG, "File created and content written: $fileName")
                    true
                } catch (e: IOException) {
                    Log.e(TAG, "Failed to create and write to file: $fileName", e)
                    false
                }
            } else {
                // 文件已存在，不做任何操作
                true
            }
        }
    }

    /**
     * 将文本内容保存到应用私有下载目录
     *
     * @param context 上下文
     * @param fileName 文件名
     * @param content 要保存的文本内容
     * @param subDirectory 子目录名称，可选参数，默认为null（直接保存到Downloads根目录）
     * @return 保存的文件路径，如果保存失败则返回null
     */
    fun saveTextToDownloads(
        context: Context,
        fileName: String,
        content: String,
        subDirectory: String? = null
    ): String? {
        try {
            // 获取应用私有下载目录
            val baseDownloadsDir = context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)
                ?: return null

            // 确定最终的目标目录
            val targetDir = if (subDirectory.isNullOrBlank()) {
                baseDownloadsDir
            } else {
                File(baseDownloadsDir, subDirectory)
            }

            // 确保目录存在
            if (!targetDir.exists()) {
                val created = targetDir.mkdirs()
                if (!created && !targetDir.exists()) {
                    Log.e(TAG, "Failed to create directory: ${targetDir.absolutePath}")
                    return null
                }
            }

            // 创建文件
            val file = File(targetDir, fileName)

            // 写入内容
            FileOutputStream(file).use { fos ->
                fos.write(content.toByteArray())
                fos.flush()
            }

            Log.d(TAG, "Content saved to private downloads: ${file.absolutePath}")
            return file.absolutePath
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save content to private downloads: $fileName", e)
            return null
        }
    }

    /**
     * 将文本内容保存到公共下载目录
     *
     * @param context 上下文
     * @param fileName 文件名
     * @param content 要保存的文本内容
     * @param subDirectory 子目录名称，可选参数，默认为null（直接保存到Downloads根目录）
     * @return 保存的文件路径，如果保存失败则返回null
     */
    fun saveTextToPublicDownloads(
        context: Context,
        fileName: String,
        content: String,
        subDirectory: String? = null
    ): String? {
        try {

            // 获取公共下载目录
            val baseDownloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)

            if (baseDownloadsDir == null) {
                Log.e(TAG, "Cannot access public downloads directory")
                return null
            }

            // 确定最终的目标目录
            val targetDir = if (subDirectory.isNullOrBlank()) {
                baseDownloadsDir
            } else {
                File(baseDownloadsDir, subDirectory)
            }

            // 确保目录存在
            if (!targetDir.exists()) {
                val created = targetDir.mkdirs()
                if (!created && !targetDir.exists()) {
                    Log.e(TAG, "Failed to create directory: ${targetDir.absolutePath}")
                    return null
                }
            }

            // 创建文件
            val file = File(targetDir, fileName)

            // 写入内容
            FileOutputStream(file).use { fos ->
                fos.write(content.toByteArray())
                fos.flush()
            }

            Log.d(TAG, "Content saved to public downloads: ${file.absolutePath}")
            return file.absolutePath
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save content to public downloads: $fileName", e)
            return null
        }
    }
}