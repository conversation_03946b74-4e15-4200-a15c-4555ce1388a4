package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.model.user.AddressItem
import dev.pigmomo.yhkit2025.api.utils.AddressUtils.matchAndAssignParamsByXYHBizParamsCommon
import dev.pigmomo.yhkit2025.api.utils.ResponseParserUtils
import dev.pigmomo.yhkit2025.viewmodel.OrderViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Locale

/**
 * 抢券工具类
 * 处理抢券相关的所有逻辑
 */
object CouponGrabbingUtils {
    private const val TAG = "CouponGrabbingUtils"

    // 创建一个协程作用域用于多线程处理
    private val couponGrabbingScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    /**
     * 开始抢券任务
     *
     * @param requestBody 请求体JSON字符串
     * @param isTimer 是否启用定时
     * @param targetTime 目标时间
     * @param multiThreadRangeList 多线程配置数组，指定要使用的orderTokens索引
     * @param viewModel 订单视图模型，用于获取orderTokens列表
     * @param selectedProxy 选择的代理配置，默认为空
     * @param context 应用上下文，用于数据库操作，默认为null
     */
    fun startCouponGrabbing(
        requestBody: String,
        isTimer: Boolean,
        targetTime: String,
        multiThreadRangeList: List<Int>,
        viewModel: OrderViewModel,
        selectedProxy: String = "",
        context: Context? = null,
        waitTime: (Long) -> Unit
    ) {
        Log.d(TAG, "Starting coupon grabbing with data: $requestBody")
        Log.d(TAG, "Timer settings: isTimer=$isTimer, targetTime=$targetTime")
        Log.d(TAG, "Multi-thread range list: $multiThreadRangeList")
        Log.d(TAG, "Proxy settings: selectedProxy=$selectedProxy")

        // 使用协程在后台线程中执行任务
        couponGrabbingScope.launch {
            try {
                // 设置抢券状态为true
                viewModel.setIsCouponGrabbing(true)

                // 获取订单令牌列表
                val orderTokensList = viewModel.orderTokens.value
                val serviceType = viewModel.serviceType.value
                val fastenAddressItem = if (viewModel.fastenAddress.value) {
                    viewModel.selectedAddressItem.value
                } else {
                    null
                }

                // 如果没有令牌或者多线程范围列表为空，则直接返回
                if (orderTokensList.isEmpty() || multiThreadRangeList.isEmpty()) {
                    Log.e(TAG, "No order tokens available or multi-thread range list is empty")
                    viewModel.setIsCouponGrabbing(false)
                    return@launch
                }

                // 初始化进度记录
                OperationProgressUtils.initProgress(
                    OperationProgressUtils.OperationType.COUPON,
                    setOf("抢券"),
                    multiThreadRangeList.size
                )

                // 如果启用定时，等待到指定时间
                if (isTimer) {
                    val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                    val targetTimeMillis =
                        dateFormat.parse(targetTime)?.time ?: System.currentTimeMillis()
                    val currentTimeMillis = System.currentTimeMillis()
                    val totalWaitTime = targetTimeMillis - currentTimeMillis

                    if (totalWaitTime > 0) {
                        // 根据是否使用代理决定提前时间
                        val advanceTime = if (selectedProxy.isNotEmpty()) 15000L else 7000L
                        val initialWaitTime = totalWaitTime - advanceTime

                        if (initialWaitTime > 0) {
                            Log.d(TAG, "Initial waiting for: ${initialWaitTime}ms")
                            waitTime(totalWaitTime)
                            delay(initialWaitTime)
                        } else {
                            Log.d(TAG, "Coupon grabbing time is over, trying to grab coupon")
                            waitTime(totalWaitTime)
                        }

                        Log.d(TAG, "Starting advance preparation phase")
                    } else {
                        Log.d(TAG, "Coupon grabbing time is over, trying to grab coupon")
                        waitTime(0)
                    }
                }

                // 创建一个列表来存储所有子任务
                val tasks = multiThreadRangeList.map { rangeIndex ->
                    // 使用 async 而不是 launch，这样可以等待所有任务完成
                    async {
                        // 根据范围列表中的索引获取对应的令牌（索引从1开始，所以需要减1）
                        val tokenIndex = rangeIndex
                        val realTokenIndex = rangeIndex - 1

                        // 检查索引是否有效
                        if (realTokenIndex < 0 || realTokenIndex >= orderTokensList.size) {
                            Log.e(
                                TAG,
                                "Invalid token index: $realTokenIndex (from range index: $tokenIndex)"
                            )
                            return@async
                        }

                        OperationProgressUtils.updateProgress()

                        // 获取对应的令牌
                        val token = orderTokensList[realTokenIndex]

                        try {
                            // 创建进度记录器，用于记录执行过程
                            val tokenProcessRecorder = ProcessRecorder(
                                tokenUid = token.uid,
                                phoneNumber = token.phoneNumber,
                                tag = TAG,
                                logLevelDefault = "INFO",
                                saveToDb = true,
                                context = context
                            )

                            Log.d(
                                TAG,
                                "Processing token: ${token.phoneNumber} (index: $rangeIndex)"
                            )

                            // 创建RequestService实例，使用serviceType
                            val requestService = RequestService.create(token, serviceType)

                            // 代理配置变量
                            var proxyConfig: HttpProxyUtils.ProxyConfig? = null
                            var realProxyIp: String = ""

                            // 提前配置代理或等待
                            if (selectedProxy.isNotEmpty()) {
                                // 检查抢券状态
                                if (checkCouponGrabbingStatus(
                                        viewModel,
                                        tokenProcessRecorder,
                                        tokenIndex,
                                        "代理准备"
                                    )
                                ) {
                                    return@async
                                }

                                tokenProcessRecorder.recordProcess("开始代理配置: $selectedProxy")
                                val proxyStartTime = System.currentTimeMillis()

                                try {
                                    proxyConfig =
                                        HttpProxyUtils.getProxyIp(selectProxyConfig = selectedProxy)

                                    if (proxyConfig.proxyInfo.first != null && proxyConfig.proxyInfo.second != null) {
                                        // 获取代理实际IP和端口
                                        val actualProxyInfo = HttpProxyUtils.getProxyIpInfo(
                                            proxyConfig.proxyInfo,
                                            proxyConfig.proxyAccount
                                        )
                                        realProxyIp = actualProxyInfo.ip

                                        if (realProxyIp.isEmpty()) {
                                            Log.e(TAG, "Failed to get actual proxy info")
                                            tokenProcessRecorder.recordProcess(
                                                "当前IP获取错误，停止执行操作",
                                                "ERROR"
                                            )
                                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                                FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                                tokenIndex,
                                                "IP获取"
                                            )
                                            OperationProgressUtils.recordFailure()
                                            return@async
                                        } else {
                                            val proxyElapsedTime =
                                                System.currentTimeMillis() - proxyStartTime
                                            tokenProcessRecorder.recordProcess("代理IP获取成功: $realProxyIp (耗时: ${proxyElapsedTime}ms)")
                                        }
                                    } else {
                                        tokenProcessRecorder.recordProcess(
                                            "代理IP获取错误，停止执行操作",
                                            "ERROR"
                                        )
                                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                            FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                            tokenIndex,
                                            "代理配置"
                                        )
                                        OperationProgressUtils.recordFailure()
                                        return@async
                                    }
                                } catch (e: Exception) {
                                    tokenProcessRecorder.recordProcess(
                                        "代理获取异常: ${e.message}",
                                        "ERROR"
                                    )
                                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                        FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                        tokenIndex,
                                        "代理获取异常"
                                    )
                                    OperationProgressUtils.recordFailure()
                                    return@async
                                }
                            }

                            // 设置代理到RequestService
                            if (selectedProxy.isNotEmpty() && proxyConfig != null) {
                                // 检查抢券状态
                                if (checkCouponGrabbingStatus(
                                        viewModel,
                                        tokenProcessRecorder,
                                        tokenIndex,
                                        "代理设置"
                                    )
                                ) {
                                    return@async
                                }

                                val success = requestService.setupProxy(proxyConfig)
                                if (success) {
                                    tokenProcessRecorder.recordProcess("代理设置成功: ${proxyConfig.proxyInfo.first}:${proxyConfig.proxyInfo.second}")
                                } else {
                                    tokenProcessRecorder.recordProcess(
                                        "代理设置错误，停止执行操作",
                                        "ERROR"
                                    )
                                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                        FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                        tokenIndex,
                                        "代理设置"
                                    )
                                    OperationProgressUtils.recordFailure()
                                    return@async
                                }
                            }

                            // 初始化必要的参数
                            var lsAddressList: List<AddressItem> = emptyList()
                            var selectedAddress: AddressItem? = null
                            var initialized = false

                            // 检查是否有固定地址
                            if (fastenAddressItem != null) {
                                // 使用固定地址
                                selectedAddress = fastenAddressItem
                                lsAddressList = listOf(fastenAddressItem)
                                tokenProcessRecorder.recordProcess("已选择固定地址: ${selectedAddress.address.area} ${selectedAddress.address.detail}")
                            } else {
                                // 获取地址列表
                                tokenProcessRecorder.recordProcess("获取地址列表")
                                val addressResult = runBlocking {
                                    requestService.address.getAllAddress()
                                }

                                when (addressResult) {
                                    is RequestResult.Success -> {
                                        val addressResponse =
                                            ResponseParserUtils.parseAddressResponse(addressResult.data)
                                        if (addressResponse != null && addressResponse.code == 0) {
                                            val addressList =
                                                addressResponse.data?.list ?: emptyList()

                                            if (addressList.isNotEmpty()) {
                                                // 全部地址
                                                lsAddressList = addressList
                                                // 选择第一个地址
                                                selectedAddress = addressList[0]
                                                tokenProcessRecorder.recordProcess("已选择地址: ${selectedAddress.address.area} ${selectedAddress.address.detail}")
                                            } else {
                                                tokenProcessRecorder.recordProcess(
                                                    "地址列表为空",
                                                    "WARNING"
                                                )
                                                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                                    FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                                    tokenIndex,
                                                    "地址列表"
                                                )
                                                OperationProgressUtils.recordFailure()
                                                return@async
                                            }
                                        } else {
                                            tokenProcessRecorder.recordProcess(
                                                "获取地址列表错误: ${addressResponse?.message ?: "未知错误"}",
                                                "ERROR"
                                            )
                                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                                FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                                tokenIndex,
                                                "地址列表"
                                            )
                                            OperationProgressUtils.recordFailure()
                                            return@async
                                        }
                                    }

                                    is RequestResult.Error -> {
                                        tokenProcessRecorder.recordProcess(
                                            "获取地址列表错误: ${addressResult.error.message}",
                                            "ERROR"
                                        )
                                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                            FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                            tokenIndex,
                                            "地址列表"
                                        )
                                        OperationProgressUtils.recordFailure()
                                        return@async
                                    }
                                }
                            }

                            // 检查是否存在固定地址的缓存参数
                            if (fastenAddressItem != null && BatchOperationUtils.AddressParamsCache.hasCache(
                                    selectedAddress.id
                                )
                            ) {
                                // 使用缓存的参数
                                val cachedParams =
                                    BatchOperationUtils.AddressParamsCache.getParams(selectedAddress.id)
                                if (cachedParams != null) {
                                    tokenProcessRecorder.recordProcess("使用固定地址缓存参数")

                                    // 设置缓存的参数
                                    requestService.setXyhBizParams(cachedParams.xyhBizParams)
                                    requestService.setWebXyhBizParams(cachedParams.webXyhBizParams)
                                    requestService.setShopId(cachedParams.shopId)
                                    requestService.setSellerId(cachedParams.sellerId)
                                    requestService.setCityId(cachedParams.cityId)
                                    requestService.setDistrict(cachedParams.district)

                                    initialized = true
                                }
                            } else {
                                // 获取店铺信息
                                val shopResult = runBlocking {
                                    requestService.shop.getFbShopLbs(selectedAddress)
                                }

                                when (shopResult) {
                                    is RequestResult.Success -> {
                                        val shopResponse =
                                            ResponseParserUtils.parseShopResponse(shopResult.data)
                                        if (shopResponse != null && shopResponse.code == 0) {
                                            val addressInfo =
                                                shopResponse.data?.address?.firstOrNull()
                                            val cityInfo = shopResponse.data?.city
                                            val shopInfoList = shopResponse.data?.seller

                                            if (addressInfo != null && cityInfo != null && !shopInfoList.isNullOrEmpty()) {
                                                val sellerId = shopInfoList[0].sellerid.toString()
                                                val shopId = shopInfoList[0].shopid

                                                // 获取XYH业务参数
                                                val addressId = addressInfo.id
                                                val lat = addressInfo.location?.lat ?: ""
                                                val lng = addressInfo.location?.lng ?: ""
                                                val cityId = cityInfo.id
                                                val district = addressInfo.address.district

                                                val xyhResult = runBlocking {
                                                    requestService.getXyhBizParams(
                                                        lat = lat,
                                                        lng = lng,
                                                        cityid = cityId,
                                                        district = district,
                                                        sellerid = sellerId,
                                                        shopid = shopId,
                                                        addressId = addressId,
                                                        serviceType = serviceType
                                                    )
                                                }

                                                when (xyhResult) {
                                                    is RequestResult.Success -> {
                                                        // 设置XYH业务参数
                                                        val xyhBizParams = xyhResult.data
                                                        requestService.setXyhBizParams(xyhBizParams)
                                                        val webXyhBizParams =
                                                            matchAndAssignParamsByXYHBizParamsCommon(
                                                                xyhBizParams
                                                            )
                                                        requestService.setWebXyhBizParams(
                                                            webXyhBizParams
                                                        )

                                                        // 设置店铺ID和销售商ID
                                                        requestService.setShopId(shopId)
                                                        requestService.setSellerId(sellerId)
                                                        requestService.setCityId(cityId)
                                                        requestService.setDistrict(district)

                                                        // 如果是固定地址，缓存参数
                                                        if (fastenAddressItem != null) {
                                                            val params =
                                                                BatchOperationUtils.AddressParamsCache.AddressParams(
                                                                    xyhBizParams = xyhBizParams,
                                                                    webXyhBizParams = webXyhBizParams,
                                                                    shopId = shopId,
                                                                    sellerId = sellerId,
                                                                    cityId = cityId,
                                                                    district = district
                                                                )
                                                            BatchOperationUtils.AddressParamsCache.saveParams(
                                                                selectedAddress.id,
                                                                params
                                                            )
                                                            tokenProcessRecorder.recordProcess("固定地址参数已缓存")
                                                        }

                                                        tokenProcessRecorder.recordProcess("XYH参数设置成功")
                                                        initialized = true
                                                    }

                                                    is RequestResult.Error -> {
                                                        tokenProcessRecorder.recordProcess(
                                                            "获取XYH业务参数错误: ${xyhResult.error.message}",
                                                            "ERROR"
                                                        )
                                                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                                            FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                                            tokenIndex,
                                                            "XYH参数"
                                                        )
                                                        OperationProgressUtils.recordFailure()
                                                        return@async
                                                    }
                                                }
                                            } else {
                                                tokenProcessRecorder.recordProcess(
                                                    "店铺数据不完整",
                                                    "WARNING"
                                                )
                                                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                                    FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                                    tokenIndex,
                                                    "店铺数据"
                                                )
                                                OperationProgressUtils.recordFailure()
                                                return@async
                                            }
                                        } else {
                                            tokenProcessRecorder.recordProcess(
                                                "获取店铺信息错误: ${shopResponse?.message ?: "未知错误"}",
                                                "ERROR"
                                            )
                                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                                FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                                tokenIndex,
                                                "店铺数据"
                                            )
                                            OperationProgressUtils.recordFailure()
                                            return@async
                                        }
                                    }

                                    is RequestResult.Error -> {
                                        tokenProcessRecorder.recordProcess(
                                            "获取店铺信息错误: ${shopResult.error.message}",
                                            "ERROR"
                                        )
                                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                            FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                            tokenIndex,
                                            "店铺数据"
                                        )
                                        OperationProgressUtils.recordFailure()
                                        return@async
                                    }
                                }
                            }

                            if (!initialized) {
                                tokenProcessRecorder.recordProcess("初始化错误", "ERROR")
                                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                    FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                    tokenIndex,
                                    "初始化错误"
                                )
                                OperationProgressUtils.recordFailure()
                                return@async
                            }

                            // 定时任务，等待到精确时间
                            if (isTimer) {
                                val dateFormat =
                                    SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                                val targetTimeMillis =
                                    dateFormat.parse(targetTime)?.time ?: System.currentTimeMillis()
                                val currentTimeMillis = System.currentTimeMillis()
                                val remainingWaitTime = targetTimeMillis - currentTimeMillis

                                if (remainingWaitTime > 0) {
                                    tokenProcessRecorder.recordProcess("等待抢券时间: ${remainingWaitTime}ms")
                                    delay(remainingWaitTime)
                                } else {
                                    tokenProcessRecorder.recordProcess("抢券时间已过，尝试抢券")
                                }
                            }

                            // 检查抢券状态
                            if (checkCouponGrabbingStatus(
                                    viewModel,
                                    tokenProcessRecorder,
                                    tokenIndex,
                                    "抢券请求"
                                )
                            ) {
                                return@async
                            }

                            // 执行抢券逻辑
                            tokenProcessRecorder.recordProcess("执行抢券请求: $requestBody")
                            Log.d(
                                TAG,
                                "Executing coupon grabbing request for token: ${token.phoneNumber}"
                            )

                            // 调用API检查活动状态
                            val result = runBlocking {
                                requestService.coupon.couponGrabbing(requestBody)
                            }

                            when (result) {
                                is RequestResult.Success -> {
                                    try {
                                        val response = result.data

                                        // 抢券结果
                                        tokenProcessRecorder.recordProcess("抢券结果: $response")
                                        OperationProgressUtils.recordSuccess()

                                    } catch (e: Exception) {
                                        // 解析响应错误
                                        tokenProcessRecorder.recordProcess(
                                            "解析响应错误: ${e.message}",
                                            "ERROR"
                                        )
                                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                            FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                            tokenIndex,
                                            "解析响应错误"
                                        )
                                        OperationProgressUtils.recordFailure()
                                    }
                                }

                                is RequestResult.Error -> {
                                    // 请求失败
                                    tokenProcessRecorder.recordProcess(
                                        "抢券请求错误: ${result.error.message}",
                                        "ERROR"
                                    )
                                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                        FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                        tokenIndex,
                                        "抢券请求错误"
                                    )
                                    OperationProgressUtils.recordFailure()
                                }
                            }

                        } catch (e: Exception) {
                            Log.e(TAG, "Error processing token at index $rangeIndex: ${e.message}")
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                                tokenIndex,
                                "未知错误"
                            )
                            OperationProgressUtils.recordFailure()
                        }
                    }
                }

                // 等待所有任务完成
                tasks.awaitAll()

                // 所有任务完成后，设置抢券状态为false
                Log.d(TAG, "All coupon grabbing tasks completed")
                viewModel.setIsCouponGrabbing(false)

            } catch (e: Exception) {
                Log.e(TAG, "Error in coupon grabbing task", e)
                viewModel.setIsCouponGrabbing(false)
            }
        }
    }

    /**
     * 检查抢券状态，如果已关闭则记录错误并返回true表示应该停止执行
     *
     * @param viewModel ViewModel实例
     * @param progressRecorder 进度记录器
     * @param tokenIndex 令牌索引
     * @param operationName 操作名称
     * @return true表示应该停止执行，false表示可以继续
     */
    private fun checkCouponGrabbingStatus(
        viewModel: OrderViewModel,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int,
        operationName: String
    ): Boolean {
        if (!viewModel.isCouponGrabbing.value) {
            Log.d(
                TAG,
                "Coupon grabbing is disabled before $operationName, cancelling task"
            )
            progressRecorder.recordProcess("抢券已关闭，停止执行操作", "ERROR")
            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                FailedTokenIndexRecordUtils.OperationType.COUPON_OPERATION,
                tokenIndex,
                operationName
            )
            OperationProgressUtils.recordFailure()
            return true
        }
        return false
    }
} 
