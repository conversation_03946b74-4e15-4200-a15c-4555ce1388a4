package dev.pigmomo.yhkit2025.utils.boostcoupon

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.utils.common.FileUtils
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.collections.iterator
import kotlin.concurrent.read
import kotlin.concurrent.write

/**
 * 游戏码管理类
 *
 * 用于管理助力券游戏码的保存、读取和解析，支持线程安全和基于时间的锁定管理
 */
object GameCodeManager {
    private const val TAG = "GameCodeManager"
    private const val GAME_CODE_FILE = "gameCodeList.txt"

    // 锁定超时时间（毫秒）- 10秒
    private const val LOCK_TIMEOUT_MS = 10_000L

    // 清理任务执行间隔（毫秒）- 3秒（增加清理频率）
    private const val CLEANUP_INTERVAL_MS = 3_000L

    // 最大重试次数
    private const val MAX_RETRY_COUNT = 3

    // 用于保护游戏码操作的读写锁
    private val gameLock = ReentrantReadWriteLock()

    // 记录正在使用中的gameCode及其锁定时间，支持基于时间的自动释放
    private val inUseGameCodes = ConcurrentHashMap<String, Long>()

    // 定时清理任务执行器
    private val cleanupExecutor: ScheduledExecutorService = Executors.newSingleThreadScheduledExecutor { r ->
        Thread(r, "GameCodeManager-Cleanup").apply {
            isDaemon = true
        }
    }

    init {
        // 启动定时清理任务
        startCleanupTask()
    }

    /**
     * 游戏码数据类
     *
     * @property timestamp 时间戳
     * @property phoneNumber 手机号码
     * @property prizeId 奖品ID
     * @property gameCode 游戏码
     */
    data class GameCodeInfo(
        val timestamp: String,
        val phoneNumber: String,
        val prizeId: String,
        val gameCode: String
    )

    /**
     * 锁定状态信息类
     *
     * @property key 锁定键
     * @property lockTime 锁定时间
     * @property remainingTime 剩余时间（毫秒）
     */
    data class LockInfo(
        val key: String,
        val lockTime: Long,
        val remainingTime: Long
    )

    /**
     * 启动定时清理任务
     */
    private fun startCleanupTask() {
        cleanupExecutor.scheduleWithFixedDelay({
            try {
                cleanupExpiredLocks()
            } catch (e: Exception) {
                Log.e(TAG, "Error during cleanup task", e)
            }
        }, CLEANUP_INTERVAL_MS, CLEANUP_INTERVAL_MS, TimeUnit.MILLISECONDS)

        Log.d(TAG, "Cleanup task started with interval: ${CLEANUP_INTERVAL_MS}ms")
    }

    /**
     * 清理过期的锁定
     */
    private fun cleanupExpiredLocks() {
        val currentTime = System.currentTimeMillis()
        val expiredKeys = mutableListOf<String>()

        // 查找过期的锁定
        inUseGameCodes.forEach { (key, lockTime) ->
            if (currentTime - lockTime > LOCK_TIMEOUT_MS) {
                expiredKeys.add(key)
            }
        }

        // 移除过期的锁定
        if (expiredKeys.isNotEmpty()) {
            expiredKeys.forEach { key ->
                inUseGameCodes.remove(key)
            }
            Log.d(TAG, "Cleaned up ${expiredKeys.size} expired locks: $expiredKeys")
        }
    }

    /**
     * 强制清理所有锁定状态（紧急恢复机制）
     *
     * @return 清理的锁定数量
     */
    fun forceCleanupAllLocks(): Int {
        val count = inUseGameCodes.size
        inUseGameCodes.clear()
        Log.w(TAG, "Force cleaned up all $count locks")
        return count
    }

    /**
     * 获取当前锁定状态信息（监控和调试功能）
     *
     * @return 锁定状态信息列表
     */
    fun getLockStatusInfo(): List<LockInfo> {
        val currentTime = System.currentTimeMillis()
        return inUseGameCodes.map { (key, lockTime) ->
            val remainingTime = maxOf(0, LOCK_TIMEOUT_MS - (currentTime - lockTime))
            LockInfo(key, lockTime, remainingTime)
        }.sortedBy { it.remainingTime }
    }

    /**
     * 获取锁定统计信息
     *
     * @return Pair<总锁定数, 即将过期数(剩余时间<3秒)>
     */
    fun getLockStatistics(): Pair<Int, Int> {
        val currentTime = System.currentTimeMillis()
        val total = inUseGameCodes.size
        val soonExpired = inUseGameCodes.count { (_, lockTime) ->
            (currentTime - lockTime) > (LOCK_TIMEOUT_MS - 3000)
        }
        return Pair(total, soonExpired)
    }

    /**
     * 关闭清理任务执行器（应用关闭时调用）
     */
    fun shutdown() {
        try {
            cleanupExecutor.shutdown()
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow()
            }
            Log.d(TAG, "Cleanup executor shutdown completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup executor shutdown", e)
            cleanupExecutor.shutdownNow()
        }
    }

    /**
     * 保存游戏码（线程安全）
     *
     * @param context 上下文
     * @param prizeId 奖品ID
     * @param gameCode 游戏码
     * @param phoneNumber 手机号码
     * @return 是否保存成功
     */
    fun saveGameCode(context: Context, prizeId: String, gameCode: String, phoneNumber: String): Boolean {
        // 使用写锁保护，确保多线程环境下的安全性
        return gameLock.write {
            val timestamp = FileUtils.getCurrentTimeStamp()
            val content = "$timestamp,$phoneNumber,$prizeId,$gameCode"

            // FileUtils本身已经是线程安全的
            FileUtils.appendToFile(context, GAME_CODE_FILE, content)
        }
    }

    /**
     * 批量保存游戏码（线程安全）
     *
     * @param context 上下文
     * @param gameCodeInfoList 游戏码信息列表
     * @return 是否全部保存成功
     */
    fun saveGameCodes(context: Context, gameCodeInfoList: List<GameCodeInfo>): Boolean {
        if (gameCodeInfoList.isEmpty()) {
            return true
        }

        return gameLock.write {
            val sb = StringBuilder()
            for (info in gameCodeInfoList) {
                sb.append("${info.timestamp},${info.phoneNumber},${info.prizeId},${info.gameCode}\n")
            }

            FileUtils.appendToFile(context, GAME_CODE_FILE, sb.toString(), false)
        }
    }

    /**
     * 获取所有游戏码信息（线程安全）
     *
     * @param context 上下文
     * @return 游戏码信息列表
     */
    fun getAllGameCodes(context: Context): List<GameCodeInfo> {
        return gameLock.read {
            val lines = FileUtils.readLines(context, GAME_CODE_FILE)
            Log.d(TAG, "getAllGameCodes: Read ${lines.size} lines from file")

            // 检查文件是否存在但为空
            if (lines.isEmpty()) {
                val fileExists = FileUtils.fileExists(context, GAME_CODE_FILE)
                val fileSize = FileUtils.getFileSize(context, GAME_CODE_FILE)
                Log.w(TAG, "getAllGameCodes: File exists=$fileExists, size=$fileSize bytes, but no lines read")
            }

            val result = parseGameCodeLines(lines)
            Log.d(TAG, "getAllGameCodes: Parsed ${result.size} valid game codes from ${lines.size} lines")
            result
        }
    }

    /**
     * 解析游戏码行数据
     *
     * @param lines 文件行数据
     * @return 游戏码信息列表
     */
    private fun parseGameCodeLines(lines: List<String>): List<GameCodeInfo> {
        val result = mutableListOf<GameCodeInfo>()
        var validLines = 0
        var invalidLines = 0
        var emptyLines = 0

        for ((index, line) in lines.withIndex()) {
            try {
                // 跳过空行
                if (line.trim().isEmpty()) {
                    emptyLines++
                    continue
                }

                val parts = line.split(",")
                if (parts.size >= 4) {
                    // 验证各个字段不为空
                    val timestamp = parts[0].trim()
                    val phoneNumber = parts[1].trim()
                    val prizeId = parts[2].trim()
                    val gameCode = parts[3].trim()

                    if (timestamp.isNotEmpty() && phoneNumber.isNotEmpty() &&
                        prizeId.isNotEmpty() && gameCode.isNotEmpty()) {
                        val gameCodeInfo = GameCodeInfo(
                            timestamp = timestamp,
                            phoneNumber = phoneNumber,
                            prizeId = prizeId,
                            gameCode = gameCode
                        )
                        result.add(gameCodeInfo)
                        validLines++
                    } else {
                        invalidLines++
                        Log.w(TAG, "parseGameCodeLines: Line ${index + 1} has empty fields: $line")
                    }
                } else {
                    invalidLines++
                    Log.w(TAG, "parseGameCodeLines: Line ${index + 1} has insufficient parts (${parts.size}): $line")
                }
            } catch (e: Exception) {
                invalidLines++
                Log.e(TAG, "parseGameCodeLines: Error parsing line ${index + 1}: $line", e)
            }
        }

        Log.d(TAG, "parseGameCodeLines: Total lines=${lines.size}, valid=$validLines, invalid=$invalidLines, empty=$emptyLines")

        if (lines.isNotEmpty() && result.isEmpty()) {
            Log.w(TAG, "parseGameCodeLines: File has ${lines.size} lines but no valid game codes parsed!")
        }

        return result
    }

    /**
     * 按奖品ID获取游戏码信息（线程安全）
     *
     * @param context 上下文
     * @param prizeId 奖品ID
     * @return 匹配该奖品ID的游戏码信息列表
     */
    fun getGameCodesByPrizeId(context: Context, prizeId: String): List<GameCodeInfo> {
        return gameLock.read {
            getAllGameCodes(context).filter { it.prizeId == prizeId }
        }
    }

    /**
     * 按手机号获取游戏码信息（线程安全）
     *
     * @param context 上下文
     * @param phoneNumber 手机号码
     * @return 匹配该手机号的游戏码信息列表
     */
    fun getGameCodesByPhoneNumber(context: Context, phoneNumber: String): List<GameCodeInfo> {
        return gameLock.read {
            getAllGameCodes(context).filter { it.phoneNumber == phoneNumber }
        }
    }

    /**
     * 获取其他账号的游戏码（线程安全）
     *
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @return 其他账号的游戏码列表
     */
    fun getOtherAccountGameCodes(context: Context, excludePhoneNumber: String): List<GameCodeInfo> {
        return gameLock.read {
            getAllGameCodes(context).filter { it.phoneNumber != excludePhoneNumber }
        }
    }

    /**
     * 获取按prizeId去重后的游戏码（线程安全）
     * 对于每个prizeId只保留时间戳最新的一个游戏码
     *
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码，可选
     * @param sortByTimestamp 是否按时间戳排序，默认为true
     * @param descending 是否降序排序，默认为true（最新的在前）
     * @return 去重后的游戏码列表，按prizeId唯一
     */
    fun getUniqueGameCodesByPrizeId(
        context: Context,
        excludePhoneNumber: String? = null,
        sortByTimestamp: Boolean = true,
        descending: Boolean = true
    ): List<GameCodeInfo> {
        return gameLock.read {
            // 获取所有游戏码
            var gameCodes = getAllGameCodes(context)

            // 如果需要排除指定手机号，则进行过滤
            if (excludePhoneNumber != null) {
                gameCodes = gameCodes.filter { it.phoneNumber != excludePhoneNumber }
            }

            // 按prizeId分组，并对每个组取时间戳最新的一个
            val uniqueCodes = gameCodes
                .groupBy { it.prizeId }
                .mapValues { (_, codeList) -> codeList.maxByOrNull { it.timestamp } }
                .values
                .filterNotNull()

            // 根据参数决定是否排序及排序方式
            if (sortByTimestamp) {
                if (descending) {
                    uniqueCodes.sortedByDescending { it.timestamp }
                } else {
                    uniqueCodes.sortedBy { it.timestamp }
                }
            } else {
                uniqueCodes.toList()
            }
        }
    }

    /**
     * 获取按prizeId分组的游戏码信息（线程安全）
     *
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码，可选
     * @return 按prizeId分组的游戏码映射
     */
    fun getGameCodesGroupedByPrizeId(
        context: Context,
        excludePhoneNumber: String? = null
    ): Map<String, List<GameCodeInfo>> {
        return gameLock.read {
            // 获取所有游戏码
            var gameCodes = getAllGameCodes(context)

            // 如果需要排除指定手机号，则进行过滤
            if (excludePhoneNumber != null) {
                gameCodes = gameCodes.filter { it.phoneNumber != excludePhoneNumber }
            }

            // 按prizeId分组
            gameCodes.groupBy { it.prizeId }
        }
    }

    /**
     * 清空所有游戏码（线程安全）
     *
     * @param context 上下文
     * @return 是否清空成功
     */
    fun clearAllGameCodes(context: Context): Boolean {
        return gameLock.write {
            FileUtils.clearFile(context, GAME_CODE_FILE)
        }
    }

    /**
     * 获取游戏码文件路径
     *
     * @param context 上下文
     * @return 游戏码文件路径
     */
    fun getGameCodeFilePath(context: Context): String {
        return FileUtils.getFilePath(context, GAME_CODE_FILE)
    }

    /**
     * 导出游戏码为CSV格式（线程安全）
     *
     * @param context 上下文
     * @return CSV格式的游戏码数据
     */
    fun exportGameCodesAsCsv(context: Context): String {
        return gameLock.read {
            val gameCodes = getAllGameCodes(context)
            val sb = StringBuilder()

            // 添加CSV头
            sb.append("时间,手机号,奖品ID,游戏码\n")

            // 添加数据行
            for (gameCode in gameCodes) {
                sb.append("${gameCode.timestamp},${gameCode.phoneNumber},${gameCode.prizeId},${gameCode.gameCode}\n")
            }

            sb.toString()
        }
    }

    /**
     * 检查游戏码是否已存在（线程安全）
     *
     * @param context 上下文
     * @param prizeId 奖品ID
     * @param gameCode 游戏码
     * @return 是否已存在
     */
    fun isGameCodeExists(context: Context, prizeId: String, gameCode: String): Boolean {
        return gameLock.read {
            getAllGameCodes(context).any { it.prizeId == prizeId && it.gameCode == gameCode }
        }
    }

    /**
     * 原子操作：获取并标记一个可用的游戏码（线程安全）
     * 确保在多线程环境下每个游戏码只被一个线程使用
     *
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码，可选
     * @return 可用的游戏码信息，如果没有可用游戏码则返回null
     */
    fun getAndMarkGameCode(context: Context, excludePhoneNumber: String? = null): GameCodeInfo? {
        try {
            // 首先使用读锁获取所有游戏码，减少写锁持有时间
            val allGameCodes = gameLock.read {
                getAllGameCodes(context)
            }

            // 如果需要排除指定手机号，则进行过滤
            var gameCodes = allGameCodes
            if (excludePhoneNumber != null) {
                gameCodes = gameCodes.filter { it.phoneNumber != excludePhoneNumber }
            }

            // 在读锁中检查可用的游戏码，按时间戳排序（最新的在前）
            val availableCodesWithKeys = gameLock.read {
                gameCodes.map {
                    val key = "${it.prizeId}:${it.gameCode}"
                    Pair(it, key)
                }.filter { (_, key) -> !inUseGameCodes.containsKey(key) }
                .sortedByDescending { (code, _) -> code.timestamp }
            }

            if (availableCodesWithKeys.isEmpty()) {
                return null
            }

            // 获取最新的一个可用游戏码（已经按时间戳排序）
            val (selectedCode, selectedKey) = availableCodesWithKeys.firstOrNull() ?: return null

            // 只在需要修改共享状态时使用写锁，并且尽量减少持有时间
            val marked = gameLock.write {
                // 再次检查是否可用，因为在获取写锁的过程中状态可能已经改变
                if (!inUseGameCodes.containsKey(selectedKey)) {
                    val currentTime = System.currentTimeMillis()
                    inUseGameCodes[selectedKey] = currentTime
                    Log.d(TAG, "Marked game code as in-use: $selectedKey at $currentTime")
                    true
                } else {
                    false
                }
            }

            return if (marked) selectedCode else null

        } catch (e: Exception) {
            Log.e(TAG, "Error getting and marking game code", e)
            return null
        }
    }

    /**
     * 释放标记为使用中的游戏码（线程安全）
     *
     * @param prizeId 奖品ID
     * @param gameCode 游戏码
     * @return 是否成功释放
     */
    fun releaseGameCode(prizeId: String, gameCode: String): Boolean {
        return gameLock.write {
            try {
                val key = "$prizeId:$gameCode"
                val lockTime = inUseGameCodes.remove(key)
                if (lockTime != null) {
                    val duration = System.currentTimeMillis() - lockTime
                    Log.d(TAG, "Released game code: $key (locked for ${duration}ms)")
                    true
                } else {
                    Log.d(TAG, "Game code was not locked or already released: $key")
                    false
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error releasing game code", e)
                false
            }
        }
    }

    /**
     * 批量释放游戏码锁定（线程安全）
     *
     * @param gameCodeInfoList 要释放的游戏码信息列表
     * @return 成功释放的数量
     */
    fun releaseGameCodes(gameCodeInfoList: List<GameCodeInfo>): Int {
        if (gameCodeInfoList.isEmpty()) return 0

        return gameLock.write {
            var releasedCount = 0
            val currentTime = System.currentTimeMillis()

            for (gameCodeInfo in gameCodeInfoList) {
                val key = "${gameCodeInfo.prizeId}:${gameCodeInfo.gameCode}"
                val lockTime = inUseGameCodes.remove(key)
                if (lockTime != null) {
                    val duration = currentTime - lockTime
                    Log.d(TAG, "Released game code: $key (locked for ${duration}ms)")
                    releasedCount++
                }
            }

            Log.d(TAG, "Batch released $releasedCount game codes")
            releasedCount
        }
    }

    /**
     * 获取并标记一个可用的游戏码，同时从指定的prizeId列表中选择（线程安全）
     *
     * @param context 上下文
     * @param prizeIds 要选择的奖品ID列表
     * @param excludePhoneNumber 要排除的手机号码，可选
     * @return 可用的游戏码信息，如果没有可用游戏码则返回null
     */
    fun getAndMarkGameCodeByPrizeIds(
        context: Context,
        prizeIds: List<String>,
        excludePhoneNumber: String? = null
    ): GameCodeInfo? {
        if (prizeIds.isEmpty()) {
            return null
        }

        try {
            // 首先使用读锁获取所有游戏码，减少写锁持有时间
            val allGameCodes = gameLock.read {
                getAllGameCodes(context)
            }

            // 如果需要排除指定手机号，则进行过滤
            var gameCodes = allGameCodes
            if (excludePhoneNumber != null) {
                gameCodes = gameCodes.filter { it.phoneNumber != excludePhoneNumber }
            }

            // 过滤出指定prizeId的游戏码
            gameCodes = gameCodes.filter { it.prizeId in prizeIds }

            // 在读锁中检查可用的游戏码，按时间戳排序（最新的在前）
            val availableCodesWithKeys = gameLock.read {
                gameCodes.map {
                    val key = "${it.prizeId}:${it.gameCode}"
                    Pair(it, key)
                }.filter { (_, key) -> !inUseGameCodes.containsKey(key) }
                .sortedByDescending { (code, _) -> code.timestamp }
            }

            if (availableCodesWithKeys.isEmpty()) {
                return null
            }

            // 获取最新的一个可用游戏码（已经按时间戳排序）
            val (selectedCode, selectedKey) = availableCodesWithKeys.firstOrNull() ?: return null

            // 只在需要修改共享状态时使用写锁，并且尽量减少持有时间
            val marked = gameLock.write {
                // 再次检查是否可用，因为在获取写锁的过程中状态可能已经改变
                if (!inUseGameCodes.containsKey(selectedKey)) {
                    val currentTime = System.currentTimeMillis()
                    inUseGameCodes[selectedKey] = currentTime
                    Log.d(TAG, "Marked game code as in-use: $selectedKey at $currentTime")
                    true
                } else {
                    false
                }
            }

            return if (marked) selectedCode else null

        } catch (e: Exception) {
            Log.e(TAG, "Error getting and marking game code by prize IDs", e)
            return null
        }
    }

    /**
     * 删除指定的游戏码，并从使用中标记中移除（线程安全）
     *
     * @param context 上下文
     * @param prizeId 奖品ID
     * @param gameCode 游戏码
     * @return 是否删除成功
     */
    fun deleteGameCode(context: Context, prizeId: String, gameCode: String): Boolean {
        return gameLock.write {
            // 从使用中标记中移除
            val key = "$prizeId:$gameCode"
            inUseGameCodes.remove(key)

            val allCodes = getAllGameCodes(context)
            val filteredCodes = allCodes.filter { !(it.prizeId == prizeId && it.gameCode == gameCode) }

            if (filteredCodes.size == allCodes.size) {
                // 没有找到要删除的游戏码
                return@write false
            }

            // 清空文件并重写所有保留的游戏码
            FileUtils.clearFile(context, GAME_CODE_FILE)

            if (filteredCodes.isEmpty()) {
                return@write true
            }

            val sb = StringBuilder()
            for (code in filteredCodes) {
                sb.append("${code.timestamp},${code.phoneNumber},${code.prizeId},${code.gameCode}\n")
            }

            FileUtils.writeToFile(context, GAME_CODE_FILE, sb.toString())
        }
    }

    /**
     * 批量删除指定的游戏码（线程安全）
     *
     * @param context 上下文
     * @param gameCodeInfoList 要删除的游戏码信息列表
     * @return 成功删除的游戏码数量
     */
    fun deleteGameCodes(context: Context, gameCodeInfoList: List<GameCodeInfo>): Int {
        if (gameCodeInfoList.isEmpty()) {
            return 0
        }

        return gameLock.write {
            val allCodes = getAllGameCodes(context)
            var deletedCount = 0

            // 创建一个集合，用于快速查找要删除的游戏码
            val toDeleteSet = gameCodeInfoList.map { "${it.prizeId}:${it.gameCode}" }.toSet()

            // 过滤出要保留的游戏码
            val filteredCodes = allCodes.filter {
                val key = "${it.prizeId}:${it.gameCode}"
                val shouldDelete = key in toDeleteSet
                if (shouldDelete) deletedCount++
                !shouldDelete
            }

            // 如果没有要删除的游戏码，直接返回
            if (deletedCount == 0) {
                return@write 0
            }

            // 清空文件并重写所有保留的游戏码
            FileUtils.clearFile(context, GAME_CODE_FILE)

            if (filteredCodes.isNotEmpty()) {
                val sb = StringBuilder()
                for (code in filteredCodes) {
                    sb.append("${code.timestamp},${code.phoneNumber},${code.prizeId},${code.gameCode}\n")
                }

                FileUtils.writeToFile(context, GAME_CODE_FILE, sb.toString())
            }

            deletedCount
        }
    }

    /**
     * 删除指定prizeId的所有游戏码（线程安全）
     *
     * @param context 上下文
     * @param prizeId 奖品ID
     * @return 成功删除的游戏码数量
     */
    fun deleteGameCodesByPrizeId(context: Context, prizeId: String): Int {
        return gameLock.write {
            val allCodes = getAllGameCodes(context)
            val codesToDelete = allCodes.filter { it.prizeId == prizeId }

            if (codesToDelete.isEmpty()) {
                return@write 0
            }

            deleteGameCodes(context, codesToDelete)
        }
    }

    /**
     * 原子操作：获取并标记多个可用的gameCode，每个prizeId一个（线程安全）
     * 确保在多线程环境下每个prizeId的游戏码只被一个线程使用
     *
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码，可选
     * @param maxCount 最大获取数量，默认为10
     * @return 可用的游戏码信息列表，如果没有可用游戏码则返回空列表
     */
    fun getAndMarkMultipleGameCodes(
        context: Context,
        excludePhoneNumber: String? = null,
        maxCount: Int = 10,
        retryOnEmpty: Boolean = true
    ): List<GameCodeInfo> {
        return getAndMarkMultipleGameCodesWithRetry(context, excludePhoneNumber, maxCount, retryOnEmpty, 0)
    }

    /**
     * 带重试计数的获取并标记多个可用的gameCode（内部方法）
     */
    private fun getAndMarkMultipleGameCodesWithRetry(
        context: Context,
        excludePhoneNumber: String? = null,
        maxCount: Int = 10,
        retryOnEmpty: Boolean = true,
        retryCount: Int = 0
    ): List<GameCodeInfo> {
        Log.d(TAG, "getAndMarkMultipleGameCodesWithRetry called: excludePhoneNumber=$excludePhoneNumber, maxCount=$maxCount, retryOnEmpty=$retryOnEmpty, retryCount=$retryCount")

        // 检查重试次数限制
        if (retryCount >= MAX_RETRY_COUNT) {
            Log.w(TAG, "Maximum retry count ($MAX_RETRY_COUNT) reached, giving up")
            return emptyList()
        }

        try {
            // 首先检查文件状态
            val fileExists = FileUtils.fileExists(context, GAME_CODE_FILE)
            val fileSize = FileUtils.getFileSize(context, GAME_CODE_FILE)
            Log.d(TAG, "File status: exists=$fileExists, size=$fileSize bytes")

            // 首先使用读锁获取所有游戏码，减少写锁持有时间
            val allGameCodes = gameLock.read {
                getAllGameCodes(context)
            }

            Log.d(TAG, "Retrieved ${allGameCodes.size} total game codes from file")

            if (allGameCodes.isEmpty()) {
                Log.w(TAG, "No game codes found in file (exists=$fileExists, size=$fileSize)")
                if (retryOnEmpty && fileSize > 0) {
                    Log.d(TAG, "File not empty but no codes parsed, retrying after 300ms delay...")
                    Thread.sleep(300)
                    return getAndMarkMultipleGameCodesWithRetry(context, excludePhoneNumber, maxCount, false, retryCount + 1)
                }
                return emptyList()
            }

            // 如果需要排除指定手机号，则进行过滤
            var gameCodes = allGameCodes
            if (excludePhoneNumber != null) {
                gameCodes = gameCodes.filter { it.phoneNumber != excludePhoneNumber }
                Log.d(TAG, "After excluding phone number $excludePhoneNumber: ${gameCodes.size} game codes remain")
            }

            // 按prizeId分组，并对每个组进行随机排序
            val gameCodesByPrizeId = gameCodes.groupBy { it.prizeId }
                .mapValues { (_, codeList) ->
                    codeList.shuffled()
                }

            Log.d(TAG, "After grouping by prizeId: ${gameCodesByPrizeId.size} unique prize groups")

            if (gameCodesByPrizeId.isEmpty()) {
                Log.w(TAG, "No game codes available after filtering and grouping")
                return emptyList()
            }

            // 在读锁中检查可用的游戏码，优先选择每个prizeId中最新的可用游戏码
            val availableCodesWithKeys = gameLock.read {
                val result = mutableListOf<Pair<GameCodeInfo, String>>()

                for ((prizeId, codeList) in gameCodesByPrizeId) {
                    Log.d(TAG, "Checking prizeId: $prizeId with ${codeList.size} codes")
                    // 对于每个prizeId，从最新的开始查找可用的游戏码
                    for ((index, code) in codeList.withIndex()) {
                        val key = "${code.prizeId}:${code.gameCode}"
                        if (!inUseGameCodes.containsKey(key)) {
                            result.add(Pair(code, key))
                            Log.d(TAG, "Found available code for $prizeId at index $index: ${code.gameCode}")
                            break // 找到该prizeId的第一个可用游戏码就跳出
                        } else {
                            Log.d(TAG, "Code at index $index is in use: ${code.gameCode}")
                        }
                    }

                    // 如果已经找到足够的游戏码，就停止
                    if (result.size >= maxCount) {
                        break
                    }
                }

                Log.d(TAG, "Found ${result.size} available codes before sorting")
                // 按时间戳排序，最新的在前
                result.sortedByDescending { (code, _) -> code.timestamp }
            }

            Log.d(TAG, "Available codes (not in use): ${availableCodesWithKeys.size} out of ${gameCodesByPrizeId.size}")

            if (availableCodesWithKeys.isEmpty()) {
                Log.w(TAG, "All game codes are currently in use")
                if (retryOnEmpty) {
                    Log.d(TAG, "All codes in use, retrying after 500ms delay...")
                    Thread.sleep(500)
                    return getAndMarkMultipleGameCodesWithRetry(context, excludePhoneNumber, maxCount, false, retryCount + 1)
                }
                return emptyList()
            }

            // 只在需要修改共享状态时使用写锁，并且尽量减少持有时间
            val markedCodes = gameLock.write {
                val result = mutableListOf<GameCodeInfo>()
                val currentTime = System.currentTimeMillis()

                for ((code, key) in availableCodesWithKeys) {
                    // 再次检查是否可用，因为在获取写锁的过程中状态可能已经改变
                    if (!inUseGameCodes.containsKey(key)) {
                        inUseGameCodes[key] = currentTime
                        result.add(code)
                        Log.d(TAG, "Marked game code as in-use: $key at $currentTime")
                    } else {
                        Log.d(TAG, "Game code became in-use while acquiring write lock: $key")
                    }
                }

                result
            }

            Log.d(TAG, "Successfully marked ${markedCodes.size} game codes as in-use")

            // 如果没有标记到游戏码但有可用游戏码，尝试再次获取
            if (markedCodes.isEmpty() && availableCodesWithKeys.isNotEmpty() && retryOnEmpty) {
                Log.d(TAG, "No codes marked but available codes exist, retrying after 200ms...")
                Thread.sleep(200)
                return getAndMarkMultipleGameCodesWithRetry(context, excludePhoneNumber, maxCount, false, retryCount + 1)
            }

            return markedCodes

        } catch (e: Exception) {
            Log.e(TAG, "Error getting and marking multiple game codes: ${e.javaClass.simpleName} - ${e.message}", e)
            return emptyList()
        }
    }

    /**
     * 检查游戏码是否被锁定
     *
     * @param key 游戏码键值 (prizeId:gameCode)
     * @return 是否被锁定
     */
    fun isGameCodeLocked(key: String): Boolean {
        return gameLock.read {
            inUseGameCodes.containsKey(key)
        }
    }

    /**
     * 获取锁定状态统计信息
     *
     * @return 锁定状态统计信息
     */
    fun getLockStatusStatistics(): LockStatusInfo {
        return gameLock.read {
            val currentTime = System.currentTimeMillis()
            var expiredCount = 0
            var oldestLockAge = 0L
            var newestLockAge = 0L

            if (inUseGameCodes.isNotEmpty()) {
                val lockTimes = inUseGameCodes.values
                oldestLockAge = currentTime - lockTimes.minOrNull()!!
                newestLockAge = currentTime - lockTimes.maxOrNull()!!

                expiredCount = inUseGameCodes.values.count { lockTime ->
                    currentTime - lockTime > LOCK_TIMEOUT_MS
                }
            }

            LockStatusInfo(
                totalLocks = inUseGameCodes.size,
                expiredLocks = expiredCount,
                activeLocks = inUseGameCodes.size - expiredCount,
                oldestLockAge = oldestLockAge,
                newestLockAge = newestLockAge
            )
        }
    }

    /**
     * 锁定状态信息数据类
     */
    data class LockStatusInfo(
        val totalLocks: Int,
        val expiredLocks: Int,
        val activeLocks: Int,
        val oldestLockAge: Long,
        val newestLockAge: Long
    )
}