package dev.pigmomo.yhkit2025.utils.common

import android.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * 加密和解密工具类
 *
 * 使用AES算法对数据进行加密和解密。
 *
 * <AUTHOR>
 */
object EncryptionUtils {

    /**
     * 加密算法
     *
     * AES/CBC/PKCS5Padding:
     * - AES: 高级加密标准，是一种对称密钥加密算法。
     * - CBC (Cipher Block Chaining): 密码块链接模式，使每个加密块依赖于前一个块，增加了安全性。
     * - PKCS5Padding: 一种填充方案，确保明文长度是块大小的整数倍。
     */
    private const val ALGORITHM = "AES/CBC/PKCS5Padding"

    /**
     * 加密密钥 (16字节)
     *
     * **安全警告**: 将密钥硬编码在代码中存在严重安全风险。
     * 在生产环境中，应使用Android Keystore系统或其他安全机制来保护密钥。
     */
    private const val SECRET_KEY = "kM9nB3vC1xZ5lK7j" // 必须是16, 24或32字节

    /**
     * 初始化向量 (IV) (16字节)
     *
     * **安全警告**: 与密钥一样，IV也不应硬编码。
     * 理想情况下，每次加密都应使用一个新的、随机生成的IV，并将其与密文一起存储或传输。
     */
    private const val IV = "pZ8uXvA2sD4fG6hJ" // 必须是16字节

    /**
     * 对给定的字符串进行加密。
     *
     * @param inputStr 需要加密的明文字符串。
     * @return Base64编码后的加密字符串。如果加密失败，则返回空字符串。
     */
    fun encrypt(inputStr: String): String {
        return try {
            // 获取Cipher实例
            val cipher = Cipher.getInstance(ALGORITHM)

            // 创建密钥规范
            val secretKeySpec = SecretKeySpec(SECRET_KEY.toByteArray(), "AES")

            // 创建IV参数规范
            val ivParameterSpec = IvParameterSpec(IV.toByteArray())

            // 初始化Cipher为加密模式
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec)

            // 执行加密操作
            val encryptedBytes = cipher.doFinal(inputStr.toByteArray())

            // 将加密后的字节数组编码为Base64字符串
            Base64.encodeToString(encryptedBytes, Base64.DEFAULT)
        } catch (e: Exception) {
            e.printStackTrace()
            // 在实际应用中，这里应该有更完善的错误处理和日志记录
            ""
        }
    }

    /**
     * 对给定的Base64编码的加密字符串进行解密。
     *
     * @param encryptedStrBase64 需要解密的Base64编码的字符串。
     * @return 解密后的明文字符串。如果解密失败，则返回空字符串。
     */
    fun decrypt(encryptedStrBase64: String): String {
        return try {
            // 获取Cipher实例
            val cipher = Cipher.getInstance(ALGORITHM)

            // 创建密钥规范
            val secretKeySpec = SecretKeySpec(SECRET_KEY.toByteArray(), "AES")

            // 创建IV参数规范
            val ivParameterSpec = IvParameterSpec(IV.toByteArray())

            // 初始化Cipher为解密模式
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec)

            // 将Base64字符串解码为字节数组
            val encryptedBytes = Base64.decode(encryptedStrBase64, Base64.DEFAULT)

            // 执行解密操作
            val decryptedBytes = cipher.doFinal(encryptedBytes)

            // 将解密后的字节数组转换为字符串
            String(decryptedBytes)
        } catch (e: Exception) {
            e.printStackTrace()
            // 在实际应用中，这里应该有更完善的错误处理和日志记录
            ""
        }
    }
}