package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.model.cart.CartItem
import dev.pigmomo.yhkit2025.api.model.cart.CartModelTypes
import dev.pigmomo.yhkit2025.api.model.cart.CartModelWrapper
import dev.pigmomo.yhkit2025.api.model.order.CouponsWidgetData
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceData
import dev.pigmomo.yhkit2025.api.model.order.OrderPlaceResponse
import dev.pigmomo.yhkit2025.api.model.order.ProductsTotalAmountWidgetData
import dev.pigmomo.yhkit2025.api.model.order.RedPacketWidgetData
import dev.pigmomo.yhkit2025.api.model.order.WidgetHelper
import dev.pigmomo.yhkit2025.api.model.user.AddressItem
import dev.pigmomo.yhkit2025.api.model.user.ShopInfo
import dev.pigmomo.yhkit2025.api.utils.AddressUtils
import dev.pigmomo.yhkit2025.api.utils.ResponseParserUtils
import dev.pigmomo.yhkit2025.ui.dialog.AutoOrderPlaceData
import dev.pigmomo.yhkit2025.ui.dialog.collectProductConfig
import dev.pigmomo.yhkit2025.utils.common.DateUtils
import dev.pigmomo.yhkit2025.viewmodel.OrderViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import kotlin.collections.forEach

/**
 * 自动下单工具类
 * 处理自动下单相关的所有逻辑
 */
object AutoOrderPlaceUtils {
    private const val TAG = "AutoOrderPlaceUtils"

    // 创建一个协程作用域用于多线程处理
    private val autoOrderScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // 添加一个同步锁对象，用于保护自动下单状态的更改
    private val autoOrderPlaceLock = Any()

    /**
     * 执行自动下单
     *
     * @param autoOrderPlaceData 自动下单数据
     * @param isAccountManager 是否启用账号配置
     * @param isAddAddress 是否启用地址配置
     * @param isAddCart 是否启用商品配置
     * @param isPlaceOrder 是否启用订单配置
     * @param multiThreadRangeList 多线程配置数组，指定要使用的orderTokens索引
     * @param viewModel 订单视图模型，用于获取orderTokens列表
     * @param selectedProxy 选择的代理配置，默认为空
     * @param context 应用上下文，用于数据库操作，默认为null
     * @param setAutoOrderPlace 设置自动下单状态的函数
     * @param operationIntervalMs 操作间隔时间（毫秒），默认为0表示无间隔
     */
    fun autoOrderPlace(
        autoOrderPlaceData: AutoOrderPlaceData,
        isAccountManager: Boolean,
        isAddAddress: Boolean,
        isAddCart: Boolean,
        isPlaceOrder: Boolean,
        multiThreadRangeList: List<Int>,
        viewModel: OrderViewModel,
        selectedProxy: String = "",
        context: Context? = null,
        setAutoOrderPlace: (Boolean) -> Unit,
        operationIntervalMs: Int = 0,
        genPayUrl: Boolean = false
    ) {
        Log.d(TAG, "Starting auto order place with data: $autoOrderPlaceData")
        Log.d(
            TAG,
            "Thread settings: isAccountManager=$isAccountManager, isAddAddress=$isAddAddress, isAddCart=$isAddCart, isPlaceOrder=$isPlaceOrder"
        )
        Log.d(TAG, "Multi-thread range list: $multiThreadRangeList")
        Log.d(TAG, "Proxy settings: selectedProxy=$selectedProxy")

        // 使用协程在后台线程中执行任务
        autoOrderScope.launch {

            // 获取订单令牌列表
            val orderTokensList = viewModel.orderTokens.value
            val serviceType = viewModel.serviceType.value

            // 获取是否固定经纬度
            val fastenLatLng = viewModel.fastenLatLng.value

            // 如果没有令牌或者多线程范围列表为空，则直接返回
            if (orderTokensList.isEmpty() || multiThreadRangeList.isEmpty()) {
                Log.e(TAG, "No order tokens available or multi-thread range list is empty")
                synchronized(autoOrderPlaceLock) {
                    setAutoOrderPlace(false)
                }
                return@launch
            }

            val operations: MutableSet<String> = mutableSetOf()
            when {
                isAccountManager -> operations.add("账号配置")
                isAddAddress -> operations.add("地址配置")
                isAddCart -> operations.add("商品配置")
                isPlaceOrder -> operations.add("订单配置")
                else -> operations
            }

            OperationProgressUtils.initProgress(
                OperationProgressUtils.OperationType.ORDER,
                operations,
                multiThreadRangeList.size
            )

            // 使用同步锁设置自动下单状态为true
            Log.d(TAG, "Setting autoOrderPlace to true")
            synchronized(autoOrderPlaceLock) {
                setAutoOrderPlace(true)
            }
            Log.d(TAG, "Set autoOrderPlace actually to ${viewModel.autoOrderPlace.value}")

            // 创建一个列表来存储所有子任务
            val tasks = mutableListOf<Deferred<Unit>>()

            // 依次启动每个账号的操作，设置间隔时间
            multiThreadRangeList.forEachIndexed { index, rangeIndex ->
                // 第一个账号立即执行，后续账号根据操作间隔错开执行
                if (index > 0 && operationIntervalMs > 0) {
                    Log.d(
                        TAG,
                        "Delaying start of ${index + 1}th token by ${operationIntervalMs}ms"
                    )
                    delay(operationIntervalMs.toLong())

                    // 延迟后再次检查自动下单状态，确保在延迟期间没有触发的暂停操作
                    val isStillOperating = viewModel.autoOrderPlace.value
                    if (!isStillOperating) {
                        Log.d(TAG, "在延迟期间检测到暂停信号，停止后续操作")
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                            rangeIndex,
                            "操作已暂停"
                        )
                        OperationProgressUtils.recordSuccess(operationIndex = rangeIndex)
                        return@launch
                    }
                }

                // 创建async任务来执行具体操作
                val task = async {
                    // 根据范围列表中的索引获取对应的令牌（索引从1开始，所以需要减1）
                    val tokenIndex = rangeIndex
                    val realTokenIndex = rangeIndex - 1

                    // 检查索引是否有效
                    if (realTokenIndex < 0 || realTokenIndex >= orderTokensList.size) {
                        Log.e(
                            TAG,
                            "Invalid token index: $realTokenIndex (from range index: $tokenIndex)"
                        )
                        return@async
                    }

                    OperationProgressUtils.updateProgress(operationIndex = tokenIndex)

                    // 获取对应的令牌
                    val token = orderTokensList[realTokenIndex]

                    try {
                        // 创建进度记录器，用于记录执行过程
                        val progressRecorder = ProcessRecorder(
                            tokenUid = token.uid,
                            phoneNumber = token.phoneNumber,
                            tag = TAG,
                            logLevelDefault = "INFO",
                            saveToDb = true,
                            context = context
                        )

                        Log.d(TAG, "Processing token: ${token.phoneNumber} (index: $rangeIndex)")

                        progressRecorder.recordProcess("批量操作开始")

                        // 创建RequestService实例，使用serviceType
                        val requestService = RequestService.create(token, serviceType)

                        // 跟踪整体操作成功状态
                        var overallSuccess = true

                        // 配置代理
                        if (selectedProxy.isNotEmpty()) {
                            // 检查自动下单状态
                            if (checkAutoOrderPlaceStatus(
                                    viewModel,
                                    progressRecorder,
                                    tokenIndex,
                                    "IP配置"
                                )
                            ) {
                                overallSuccess = false
                                OperationProgressUtils.recordFailure(operationIndex = rangeIndex)
                                return@async
                            }

                            progressRecorder.recordProcess("代理配置: $selectedProxy")
                            val proxyConfig =
                                HttpProxyUtils.getProxyIp(selectProxyConfig = selectedProxy)

                            if (proxyConfig.proxyInfo.first != null && proxyConfig.proxyInfo.second != null) {
                                // 获取代理实际IP和端口
                                val actualProxyInfo = HttpProxyUtils.getProxyIpInfo(
                                    proxyConfig.proxyInfo,
                                    proxyConfig.proxyAccount
                                )
                                val realProxyIp = actualProxyInfo.ip

                                if (realProxyIp.isEmpty()) {
                                    Log.e(TAG, "Failed to get actual proxy info")
                                    progressRecorder.recordProcess(
                                        "当前IP获取错误，停止执行操作",
                                        "ERROR"
                                    )
                                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                        tokenIndex,
                                        "IP配置"
                                    )
                                    overallSuccess = false
                                    OperationProgressUtils.recordFailure(operationIndex = rangeIndex)
                                    return@async
                                } else {
                                    progressRecorder.recordProcess("当前IP: $realProxyIp")
                                }

                                val success = requestService.setupProxy(proxyConfig)
                                if (success) {
                                    progressRecorder.recordProcess("代理设置成功: ${proxyConfig.proxyInfo.first}:${proxyConfig.proxyInfo.second}")
                                } else {
                                    progressRecorder.recordProcess(
                                        "代理设置错误，停止执行操作",
                                        "ERROR"
                                    )
                                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                        tokenIndex,
                                        "代理设置"
                                    )
                                    overallSuccess = false
                                    OperationProgressUtils.recordFailure(operationIndex = rangeIndex)
                                    return@async
                                }
                            } else {
                                progressRecorder.recordProcess("代理IP获取错误，停止执行操作", "ERROR")
                                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                    tokenIndex,
                                    "代理设置"
                                )
                                overallSuccess = false
                                OperationProgressUtils.recordFailure(operationIndex = rangeIndex)
                                return@async
                            }
                        } else {
                            // 即使不使用代理，也检查当前IP
                            val actualProxyInfo = HttpProxyUtils.getProxyIpInfo()
                            val realProxyIp = actualProxyInfo.ip

                            if (realProxyIp.isEmpty()) {
                                Log.e(TAG, "Failed to get IP info")
                                progressRecorder.recordProcess(
                                    "当前IP获取错误，停止执行操作",
                                    "ERROR"
                                )
                                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                    tokenIndex,
                                    "IP获取"
                                )
                                overallSuccess = false
                                OperationProgressUtils.recordFailure(operationIndex = rangeIndex)
                                return@async
                            } else {
                                progressRecorder.recordProcess("当前IP: $realProxyIp")
                            }
                        }

                        // 按照顺序执行流程，每个步骤前检查自动下单状态
                        if (isAccountManager) {
                            // 检查自动下单状态
                            if (checkAutoOrderPlaceStatus(
                                    viewModel,
                                    progressRecorder,
                                    tokenIndex,
                                    "账号管理"
                                )
                            ) {
                                overallSuccess = false
                                OperationProgressUtils.recordFailure(operationIndex = rangeIndex)
                                return@async
                            }

                            val accountManagerSuccess = handleAccountManager(
                                requestService,
                                autoOrderPlaceData,
                                progressRecorder,
                                tokenIndex
                            )
                            if (!accountManagerSuccess) {
                                overallSuccess = false
                            }
                        }

                        if (isAddAddress) {
                            // 检查自动下单状态
                            if (checkAutoOrderPlaceStatus(
                                    viewModel,
                                    progressRecorder,
                                    tokenIndex,
                                    "添加地址"
                                )
                            ) {
                                overallSuccess = false
                                OperationProgressUtils.recordFailure(operationIndex = rangeIndex)
                                return@async
                            }

                            val addAddressSuccess = handleAddAddress(
                                requestService,
                                autoOrderPlaceData,
                                progressRecorder,
                                fastenLatLng,
                                token.phoneNumber,
                                tokenIndex
                            )
                            if (!addAddressSuccess) {
                                overallSuccess = false
                            }
                        }

                        var selectedAddressItem: AddressItem? = null

                        if (isAddCart || isPlaceOrder) {
                            // 检查自动下单状态
                            if (checkAutoOrderPlaceStatus(
                                    viewModel,
                                    progressRecorder,
                                    tokenIndex,
                                    "地址同步"
                                )
                            ) {
                                overallSuccess = false
                                OperationProgressUtils.recordFailure(operationIndex = rangeIndex)
                                return@async
                            }

                            //同步地址
                            selectedAddressItem =
                                syncAddress(
                                    requestService,
                                    autoOrderPlaceData,
                                    progressRecorder,
                                    tokenIndex
                                )
                            if (selectedAddressItem == null) {
                                overallSuccess = false
                            }
                        }

                        if (selectedAddressItem != null && isAddCart) {
                            // 检查自动下单状态
                            if (checkAutoOrderPlaceStatus(
                                    viewModel,
                                    progressRecorder,
                                    tokenIndex,
                                    "添加购物车"
                                )
                            ) {
                                overallSuccess = false
                                OperationProgressUtils.recordFailure(operationIndex = rangeIndex)
                                return@async
                            }

                            val addCartSuccess = handleAddCart(
                                requestService,
                                autoOrderPlaceData,
                                progressRecorder,
                                selectedAddressItem,
                                tokenIndex
                            )
                            if (!addCartSuccess) {
                                overallSuccess = false
                            }
                        }

                        var currentCartItem: CartItem? = null

                        if (selectedAddressItem != null && isPlaceOrder) {
                            // 检查自动下单状态
                            if (checkAutoOrderPlaceStatus(
                                    viewModel,
                                    progressRecorder,
                                    tokenIndex,
                                    "购物车同步"
                                )
                            ) {
                                overallSuccess = false
                                OperationProgressUtils.recordFailure(operationIndex = rangeIndex)
                                return@async
                            }

                            currentCartItem = syncCart(
                                requestService,
                                autoOrderPlaceData,
                                progressRecorder,
                                selectedAddressItem,
                                tokenIndex
                            )
                            if (currentCartItem == null) {
                                overallSuccess = false
                            }
                        }

                        if (selectedAddressItem != null && currentCartItem != null) {
                            // 检查自动下单状态
                            if (checkAutoOrderPlaceStatus(
                                    viewModel,
                                    progressRecorder,
                                    tokenIndex,
                                    "下单"
                                )
                            ) {
                                overallSuccess = false
                                OperationProgressUtils.recordFailure(operationIndex = rangeIndex)
                                return@async
                            }

                            val placeOrderSuccess = handlePlaceOrder(
                                requestService,
                                autoOrderPlaceData,
                                progressRecorder,
                                selectedAddressItem,
                                currentCartItem,
                                tokenIndex,
                                genPayUrl
                            )
                            if (!placeOrderSuccess) {
                                overallSuccess = false
                            }
                        }

                        // 在所有步骤完成后，根据整体成功状态记录结果
                        if (overallSuccess) {
                            OperationProgressUtils.recordSuccess(operationIndex = tokenIndex)
                        } else {
                            OperationProgressUtils.recordFailure(operationIndex = tokenIndex)
                        }

                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing token at index $rangeIndex: ${e.message}")
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                            tokenIndex,
                            "未知错误"
                        )
                        OperationProgressUtils.recordFailure(operationIndex = tokenIndex)
                    }
                }

                // 将任务添加到列表中
                tasks.add(task)
            }

            // 等待所有任务完成
            tasks.awaitAll()

            // 所有任务完成后，再设置自动下单状态为false
            Log.d(TAG, "All auto order place tasks completed")
            Log.d(TAG, "Setting autoOrderPlace to false")
            synchronized(autoOrderPlaceLock) {
                setAutoOrderPlace(false)
            }
            Log.d(TAG, "Set autoOrderPlace actually to ${viewModel.autoOrderPlace.value}")
        }
    }

    /**
     * 处理账号管理相关操作
     *
     * @param requestService API服务
     * @param autoOrderPlaceData 自动下单数据
     * @param progressRecorder 进度记录器
     * @return 是否成功
     */
    private suspend fun handleAccountManager(
        requestService: RequestService,
        autoOrderPlaceData: AutoOrderPlaceData,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): Boolean {
        Log.d(TAG, "Handling account manager")

        var overallSuccess = true

        // 检查领取优惠券配置
        if (autoOrderPlaceData.couponPromotionCodeStr.isNotEmpty()) {
            try {
                val promotionCodeList = autoOrderPlaceData.couponPromotionCodeStr.split(";")
                var successCount = 0
                var errorCount = 0
                promotionCodeList.forEach { promotionCode ->
                    if (promotionCode.isBlank()) {
                        return@forEach
                    }

                    // 调用API领取优惠券
                    val result = withContext(Dispatchers.IO) {
                        requestService.coupon.kindCoupon(promotionCode)
                    }

                    when (result) {
                        is RequestResult.Success -> {
                            val response = ResponseParserUtils.parseKindCouponResponse(result.data)
                            withContext(Dispatchers.Main) {
                                if (response?.code == 0) {
                                    progressRecorder.recordProcess("领取优惠券成功: $promotionCode")
                                    successCount++
                                } else {
                                    progressRecorder.recordProcess(
                                        "领取优惠券错误: $promotionCode, ${response?.message}",
                                        "ERROR"
                                    )
                                    Log.e(
                                        "OrderViewModel",
                                        "Failed to receive kind coupon: ${response?.message}"
                                    )
                                    errorCount++
                                }
                            }
                        }

                        is RequestResult.Error -> {
                            progressRecorder.recordProcess(
                                "领取优惠券异常: ${result.error.message}",
                                "ERROR"
                            )
                            Log.e(
                                "OrderViewModel",
                                "Failed to receive kind coupon: ${result.error.message}"
                            )
                            errorCount++
                        }
                    }
                }

                if (successCount != promotionCodeList.size) {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                        tokenIndex,
                        "优惠券领取"
                    )
                    overallSuccess = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception while getting kind coupon", e)
                progressRecorder.recordProcess("优惠券领取异常:${e.message}", "ERROR")
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                    tokenIndex,
                    "优惠券领取"
                )
                overallSuccess = false
            }
        } else {
            Log.d(TAG, "No kind coupon shop parameters provided")
        }

        // 检查是否有新人优惠券配置
        if (autoOrderPlaceData.newPersonCouponShopParams.isNotEmpty()) {
            try {
                // 解析店铺参数
                val shopParams = autoOrderPlaceData.newPersonCouponShopParams.split(",")
                if (shopParams.size >= 2) {
                    val sellerId = shopParams[0].trim()
                    val shopId = shopParams[1].trim()

                    Log.d(
                        TAG,
                        "Trying to get new person coupon with sellerId: $sellerId, shopId: $shopId"
                    )
                    progressRecorder.recordProcess("新人券店铺参数 $sellerId,$shopId")

                    // 调用API领取新人优惠券
                    val result = withContext(Dispatchers.IO) {
                        requestService.coupon.getNewPersonPopup(sellerId, shopId)
                    }

                    when (result) {
                        is RequestResult.Success -> {
                            val response =
                                ResponseParserUtils.parseNewPersonPopupResponse(result.data)
                            if (response?.code == 0) {
                                val totalAmount = response.data?.totalAmount
                                if (totalAmount != 0) {
                                    Log.d(TAG, "Successfully got new person coupon: $totalAmount")
                                    progressRecorder.recordProcess("领取成功/${(totalAmount ?: 0) / 100}")
                                } else {
                                    Log.d(TAG, "Already claimed coupon/blacklisted/old account")
                                    progressRecorder.recordProcess("已领/黑号/老号")
                                }
                            } else {
                                Log.e(
                                    TAG,
                                    "Failed to get new person popup: ${response?.message}"
                                )
                                progressRecorder.recordProcess(
                                    "新人券领取错误:${response?.message}",
                                    "ERROR"
                                )
                                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                    tokenIndex,
                                    "新人券领取"
                                )
                                overallSuccess = false
                            }
                        }

                        is RequestResult.Error -> {
                            Log.e(
                                TAG,
                                "Failed to get new person popup: ${result.error.message}"
                            )
                            progressRecorder.recordProcess(
                                "新人券领取异常:${result.error.message?.take(15)}",
                                "ERROR"
                            )
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                tokenIndex,
                                "新人券领取"
                            )
                            overallSuccess = false
                        }
                    }
                } else {
                    Log.e(
                        TAG,
                        "Invalid shop parameters format: ${autoOrderPlaceData.newPersonCouponShopParams}"
                    )
                    progressRecorder.recordProcess("新人券店铺参数格式错误", "ERROR")
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                        tokenIndex,
                        "新人券领取"
                    )
                    overallSuccess = false
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception while getting new person coupon", e)
                progressRecorder.recordProcess("新人券领取异常:${e.message?.take(15)}", "ERROR")
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                    tokenIndex,
                    "新人券领取"
                )
                overallSuccess = false
            }
        } else {
            Log.d(TAG, "No new person coupon shop parameters provided")
        }

        // TODO: 实现其他账号管理相关操作
        return overallSuccess
    }

    /**
     * 处理添加地址相关操作
     *
     * @param requestService API服务
     * @param autoOrderPlaceData 自动下单数据
     * @param progressRecorder 进度记录器
     * @return 是否成功
     */
    private suspend fun handleAddAddress(
        requestService: RequestService,
        autoOrderPlaceData: AutoOrderPlaceData,
        progressRecorder: ProcessRecorder,
        fastenLatLng: Boolean,
        phoneNumber: String,
        tokenIndex: Int
    ): Boolean {
        Log.d(TAG, "Handling add address")

        // 检查地址配置是否存在
        if (autoOrderPlaceData.addAddressStr.isEmpty() || autoOrderPlaceData.addAddressLocationStr.isEmpty()) {
            progressRecorder.recordProcess("地址配置为空", "WARNING")
            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                tokenIndex,
                "添加地址"
            )
            return false
        }

        try {
            // 使用AddressUtils替换详细地址
            var addressStr = AddressUtils.replaceDetail(
                autoOrderPlaceData.addAddressStr,
                autoOrderPlaceData.randomDetailsRegex.replace(" ", "")
            )
            var locationStr = autoOrderPlaceData.addAddressLocationStr

            // 如果不固定经纬度，则随机化经纬度
            if (!fastenLatLng) {
                try {
                    val locationJSON = JSONObject(locationStr)
                    val lat = locationJSON.getString("lat")
                    val lng = locationJSON.getString("lng")
                    locationStr = locationStr.replace(
                        lat,
                        AddressUtils.adjustLastThreeDigits(lat)
                    )
                    locationStr = locationStr.replace(
                        lng,
                        AddressUtils.adjustLastThreeDigits(lng)
                    )
                } catch (e: Exception) {
                    Log.e(TAG, "经纬度参数格式错误", e)
                    progressRecorder.recordProcess("经纬度参数格式错误", "ERROR")
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                        tokenIndex,
                        "添加地址"
                    )
                    return false
                }
            }

            // 替换UUID
            addressStr = AddressUtils.replaceUUID(addressStr)
            locationStr = AddressUtils.replaceUUID(locationStr)

            // 随机生成姓名
            val name = AddressUtils.randomName()

            val phone = phoneNumber

            progressRecorder.recordProcess("添加地址: $name,$phone,$addressStr,$locationStr")

            // 调用API添加地址
            val result = withContext(Dispatchers.IO) {
                requestService.address.addAddress(
                    addressStr,
                    locationStr,
                    name,
                    phone
                )
            }

            when (result) {
                is RequestResult.Success -> {
                    val response = ResponseParserUtils.parseResponse<Map<String, Any>>(result.data)
                    if (response != null && response.isSuccess) {
                        Log.d(TAG, "Successfully added address")
                        progressRecorder.recordProcess("地址添加成功")
                        return true
                    } else {
                        Log.e(TAG, "Failed to add address: ${response?.errorMessage}")
                        progressRecorder.recordProcess(
                            "地址添加错误:${
                                response?.errorMessage
                            }", "ERROR"
                        )
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                            tokenIndex,
                            "添加地址"
                        )
                        return false
                    }
                }

                is RequestResult.Error -> {
                    Log.e(TAG, "Failed to add address", result.error)
                    progressRecorder.recordProcess(
                        "地址添加错误:${result.error.message}",
                        "ERROR"
                    )
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                        tokenIndex,
                        "添加地址"
                    )
                    return false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error adding address", e)
            progressRecorder.recordProcess("地址添加异常:${e.message}", "ERROR")
            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                tokenIndex,
                "添加地址"
            )
            return false
        }
    }

    /**
     * 处理添加购物车相关操作
     *
     * @param requestService API服务
     * @param autoOrderPlaceData 自动下单数据
     * @param progressRecorder 进度记录器
     * @param selectedAddressItem 选中的地址项
     * @return 是否成功
     */
    private suspend fun handleAddCart(
        requestService: RequestService,
        autoOrderPlaceData: AutoOrderPlaceData,
        progressRecorder: ProcessRecorder,
        selectedAddressItem: AddressItem,
        tokenIndex: Int
    ): Boolean {
        Log.d(TAG, "Handling add cart")

        // 检查商品配置是否存在
        if (autoOrderPlaceData.productConfigStr.isEmpty()) {
            progressRecorder.recordProcess("商品配置为空", "WARNING")
            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                tokenIndex,
                "添加购物车"
            )
            return false
        }

        progressRecorder.recordProcess("添加购物车开始")

        try {
            // 解析商品配置字符串
            val productConfigStr = autoOrderPlaceData.productConfigStr

            // 验证输入格式
            if (productConfigStr.isBlank()) {
                progressRecorder.recordProcess("商品配置不能为空", "WARNING")
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                    tokenIndex,
                    "添加购物车"
                )
                return false
            }

            if (!productConfigStr.contains(";") && !productConfigStr.contains(",")) {
                progressRecorder.recordProcess("商品配置格式错误", "ERROR")
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                    tokenIndex,
                    "添加购物车"
                )
                return false
            }

            val productConfigArr = productConfigStr.split(";")

            var successCount = 0
            var failCount = 0

            // 遍历商品配置数组，逐个添加商品
            for (productConfig in productConfigArr) {
                if (productConfig.isBlank()) {
                    continue
                }

                // 调用CartService的addCartGoods方法添加单个商品到购物车
                val result = requestService.cart.addCartGoods(productConfig, selectedAddressItem)

                when (result) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseResponse<Map<String, Any>>(result.data)
                        if (response != null && response.isSuccess) {
                            Log.d(TAG, "Successfully added product to cart: $productConfig")
                            progressRecorder.recordProcess("商品添加成功: $productConfig")
                            successCount++
                        } else {
                            Log.e(
                                TAG,
                                "Failed to add product to cart: $productConfig, ${response?.errorMessage}"
                            )
                            progressRecorder.recordProcess(
                                "商品添加错误: $productConfig ${
                                    response?.errorMessage
                                }", "ERROR"
                            )
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                tokenIndex,
                                "添加购物车"
                            )
                            failCount++
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e(TAG, "Failed to add product to cart: $productConfig", result.error)
                        progressRecorder.recordProcess(
                            "商品添加异常: ${
                                result.error.message
                            }", "ERROR"
                        )
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                            tokenIndex,
                            "添加购物车"
                        )
                        failCount++
                    }
                }
            }

            // 记录添加结果
            if (successCount > 0) {
                progressRecorder.recordProcess("成功添加${successCount}个商品" + (if (failCount > 0) "，${failCount}个失败" else ""))
                return true
            } else {
                progressRecorder.recordProcess("商品添加全部错误", "ERROR")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "Adding products to cart failed", e)
            progressRecorder.recordProcess("添加购物车异常: ${e.message}", "ERROR")
            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                tokenIndex,
                "添加购物车"
            )
            return false
        }
    }

    /**
     * 处理下单相关操作
     *
     * @param requestService API服务
     * @param autoOrderPlaceData 自动下单数据
     * @param progressRecorder 进度记录器
     * @return 是否成功
     */
    private suspend fun handlePlaceOrder(
        requestService: RequestService,
        autoOrderPlaceData: AutoOrderPlaceData,
        progressRecorder: ProcessRecorder,
        selectedAddressItem: AddressItem,
        currentCartItem: CartItem,
        tokenIndex: Int,
        genPayUrl: Boolean
    ): Boolean {
        Log.d(TAG, "Handling place order")

        try {
            progressRecorder.recordProcess("开始下单流程")

            // 第一步：调用OrderPlace接口获取订单信息
            val orderPlaceResponse = getInitialOrderData(
                requestService,
                selectedAddressItem,
                currentCartItem,
                progressRecorder,
                tokenIndex
            ) ?: run {
                return false
            }

            var orderPlaceData = orderPlaceResponse.data ?: run {
                progressRecorder.recordProcess("订单数据为空", "WARNING")
                return false
            }

            // 检查订单金额是否符合预期
            if (!validateProductTotalAmount(
                    autoOrderPlaceData,
                    orderPlaceData,
                    progressRecorder,
                    tokenIndex
                )
            ) {
                progressRecorder.recordProcess("商品金额不符", "ERROR")
                return false
            }

            // 第二步：应用用户配置的选项
            // 1. 检查并设置自提/配送选项
            if (orderPlaceData.ispickself != autoOrderPlaceData.isPickSelf) {
                orderPlaceData = updateDeliveryOption(
                    requestService,
                    selectedAddressItem,
                    currentCartItem,
                    autoOrderPlaceData,
                    orderPlaceData,
                    progressRecorder,
                    tokenIndex
                ) ?: run {
                    return false
                }
            }

            // 2. 检查并设置优惠券
            orderPlaceData = updateCouponSelection(
                requestService,
                selectedAddressItem,
                currentCartItem,
                autoOrderPlaceData,
                orderPlaceData,
                progressRecorder,
                tokenIndex
            ) ?: run {
                return false
            }

            // 3. 检查并设置红包
            orderPlaceData = updateRedPacketSelection(
                requestService,
                selectedAddressItem,
                currentCartItem,
                autoOrderPlaceData,
                orderPlaceData,
                progressRecorder,
                tokenIndex
            ) ?: run {
                return false
            }

            // 4. 检查并设置积分支付选项
            if (orderPlaceData.pointpayoption != autoOrderPlaceData.pointPayOption) {
                orderPlaceData = updatePointPayOption(
                    requestService,
                    selectedAddressItem,
                    currentCartItem,
                    autoOrderPlaceData,
                    orderPlaceData,
                    progressRecorder,
                    tokenIndex
                ) ?: run {
                    return false
                }
            }

            // 5. 检查并设置余额支付选项
            if (orderPlaceData.balancepayoption != autoOrderPlaceData.balancePayOption) {
                orderPlaceData = updateBalancePayOption(
                    requestService,
                    selectedAddressItem,
                    currentCartItem,
                    autoOrderPlaceData,
                    orderPlaceData,
                    progressRecorder,
                    tokenIndex
                ) ?: run {
                    return false
                }
            }

            // 6. 校验支付总金额
            if (!validateTotalPayment(
                    autoOrderPlaceData,
                    orderPlaceData,
                    progressRecorder,
                    tokenIndex
                )
            ) {
                return false
            }

            // 第三步：确认订单
            val orderConfirmSuccess = confirmOrder(
                requestService,
                autoOrderPlaceData,
                orderPlaceData,
                progressRecorder,
                tokenIndex,
                genPayUrl
            )

            return orderConfirmSuccess

        } catch (e: Exception) {
            Log.e(TAG, "Error in place order process", e)
            progressRecorder.recordProcess("下单过程异常: ${e.message?.take(15)}", "ERROR")
            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                tokenIndex,
                "下单"
            )
            return false
        }
    }

    /**
     * 获取初始订单数据
     */
    private suspend fun getInitialOrderData(
        requestService: RequestService,
        selectedAddressItem: AddressItem,
        currentCartItem: CartItem,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): OrderPlaceResponse? {
        val orderPlaceResult = withContext(Dispatchers.IO) {
            requestService.order.orderPlace(
                selectedAddress = selectedAddressItem,
                selectedCart = currentCartItem,
                firstOrderPlace = true,
                onRefreshOrderPlaceParams = { url, orderPlaceBody ->
                    requestService.setOrderPlaceUrl(url)
                    requestService.setOrderPlaceBody(orderPlaceBody)
                }
            )
        }

        return when (orderPlaceResult) {
            is RequestResult.Success -> {
                val orderPlaceResponse =
                    ResponseParserUtils.parseOrderPlaceResponse(orderPlaceResult.data)
                if (orderPlaceResponse != null && orderPlaceResponse.code == 0) {
                    progressRecorder.recordProcess("首次OrderPlace成功")
                    orderPlaceResponse
                } else {
                    progressRecorder.recordProcess(
                        "OrderPlace错误: ${orderPlaceResponse?.message ?: "未知错误"}",
                        "ERROR"
                    )
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                        tokenIndex,
                        "OrderPlace"
                    )
                    null
                }
            }

            is RequestResult.Error -> {
                progressRecorder.recordProcess(
                    "OrderPlace请求异常: ${orderPlaceResult.error.message?.take(15)}", "ERROR"
                )
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                    tokenIndex,
                    "OrderPlace"
                )
                null
            }
        }
    }

    /**
     * 验证商品总金额
     */
    private fun validateProductTotalAmount(
        autoOrderPlaceData: AutoOrderPlaceData,
        orderPlaceData: OrderPlaceData,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): Boolean {
        if (autoOrderPlaceData.productsTotalAmount.isEmpty()) {
            return true
        }

        var productTotal = "0"
        // 从价格信息组件中获取商品总金额数据
        val priceInfo = WidgetHelper.getPriceInfoGroupData(orderPlaceData)
        val productsTotalAmountWidgetData =
            WidgetHelper.getSubWidgetDataFromGroup<ProductsTotalAmountWidgetData>(
                priceInfo,
                "productstotalamount"
            )

        if (productsTotalAmountWidgetData != null) {
            productTotal = productsTotalAmountWidgetData.desc ?: "0"
        }

        if (productTotal != autoOrderPlaceData.productsTotalAmount) {
            progressRecorder.recordProcess(
                "商品金额不符: 期望${autoOrderPlaceData.productsTotalAmount}，实际$productTotal",
                "WARNING"
            )
            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                tokenIndex,
                "商品总金额校验"
            )
            return false
        }
        return true
    }

    /**
     * 更新配送选项
     */
    private suspend fun updateDeliveryOption(
        requestService: RequestService,
        selectedAddressItem: AddressItem,
        currentCartItem: CartItem,
        autoOrderPlaceData: AutoOrderPlaceData,
        orderPlaceData: OrderPlaceData,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): OrderPlaceData? {
        progressRecorder.recordProcess("修改配送方式: ${if (autoOrderPlaceData.isPickSelf == 1) "自提" else "配送"}")

        return submitOrderPlaceUpdate(
            requestService,
            selectedAddressItem,
            currentCartItem,
            progressRecorder,
            ispickself = autoOrderPlaceData.isPickSelf.toString(),
            successMessage = "配送方式修改成功",
            errorMessage = "配送方式修改错误",
            exceptionMessage = "配送方式修改异常",
            tokenIndex = tokenIndex
        )
    }

    /**
     * 更新优惠券选择
     */
    private suspend fun updateCouponSelection(
        requestService: RequestService,
        selectedAddressItem: AddressItem,
        currentCartItem: CartItem,
        autoOrderPlaceData: AutoOrderPlaceData,
        orderPlaceData: OrderPlaceData,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): OrderPlaceData? {
        // 从价格信息组件中获取优惠券数据
        val priceInfo = WidgetHelper.getPriceInfoGroupData(orderPlaceData)
        val couponsWidgetData =
            WidgetHelper.getSubWidgetDataFromGroup<CouponsWidgetData>(
                priceInfo,
                "coupons"
            ) ?: run {
                progressRecorder.recordProcess("可选择优惠券列表为空", "WARNING")
                // 如果用户配置了优惠券但优惠券列表为空
                if (autoOrderPlaceData.selectedOrderPlaceCoupon != null) {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                        tokenIndex,
                        "优惠券选择"
                    )
                    progressRecorder.recordProcess(
                        "可选择优惠券列表为空但已配置优惠券参数",
                        "ERROR"
                    )
                    return null
                }
                progressRecorder.recordProcess("可选择优惠券列表为空但未配置优惠券参数", "WARNING")
                return orderPlaceData
            }

        // 获取当前红包数据，用于保持选择状态
        val redPacketWidgetData =
            WidgetHelper.getSubWidgetDataFromGroup<RedPacketWidgetData>(
                priceInfo,
                "redpacket"
            )

        // 从优惠券数据中获取选中的优惠券代码
        val selectedCouponCode = couponsWidgetData.selectedcoupons?.firstOrNull() ?: ""

        // 用户配置了优惠券
        if (autoOrderPlaceData.selectedOrderPlaceCoupon != null) {
            val selectedOrderPlaceCoupon = autoOrderPlaceData.selectedOrderPlaceCoupon
            progressRecorder.recordProcess("选择优惠券: ${selectedOrderPlaceCoupon.showDescription}")

            // 从优惠券数据中获取已选中的优惠券
            val selectedCoupon =
                couponsWidgetData.availablecoupons?.firstOrNull { it.code == selectedCouponCode }

            // 检查已选中的优惠券是否与用户配置的优惠券相同
            if (selectedCoupon?.showDescription == selectedOrderPlaceCoupon.showDescription &&
                selectedCoupon.amount == selectedOrderPlaceCoupon.amount &&
                selectedCoupon.conditiondesc == selectedOrderPlaceCoupon.conditiondesc
            ) {
                progressRecorder.recordProcess("已选择优惠券: ${selectedOrderPlaceCoupon.showDescription}")
                return orderPlaceData
            }

            // 从优惠券数据中获取优惠券代码
            val couponCode = couponsWidgetData.availablecoupons?.find {
                it.showDescription == selectedOrderPlaceCoupon.showDescription &&
                        it.amount == selectedOrderPlaceCoupon.amount &&
                        it.conditiondesc == selectedOrderPlaceCoupon.conditiondesc
            }?.code ?: run {
                progressRecorder.recordProcess(
                    "未匹配到优惠券: ${selectedOrderPlaceCoupon.showDescription}",
                    "WARNING"
                )
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                    tokenIndex,
                    "优惠券选择"
                )
                return null
            }

            return submitOrderPlaceUpdate(
                requestService,
                selectedAddressItem,
                currentCartItem,
                progressRecorder,
                selectedCouponCodeArr = "[\"${couponCode}\"]",
                selectedRedPacketCodeArr = if (redPacketWidgetData?.selectedredpackets?.isNotEmpty() == true)
                    "[\"${redPacketWidgetData.selectedredpackets[0]}\"]" else "",
                ispickself = orderPlaceData.ispickself.toString(),
                successMessage = "优惠券选择成功",
                errorMessage = "优惠券选择错误",
                exceptionMessage = "优惠券选择异常",
                tokenIndex = tokenIndex
            )
        } else if (selectedCouponCode.isNotEmpty()) {
            // 用户没有配置优惠券，但当前有选中的优惠券，需要取消选择
            progressRecorder.recordProcess("取消优惠券选择")

            return submitOrderPlaceUpdate(
                requestService,
                selectedAddressItem,
                currentCartItem,
                progressRecorder,
                selectedCouponCodeArr = "",
                selectedRedPacketCodeArr = if (redPacketWidgetData?.selectedredpackets?.isNotEmpty() == true)
                    "[\"${redPacketWidgetData.selectedredpackets[0]}\"]" else "",
                ispickself = orderPlaceData.ispickself.toString(),
                successMessage = "优惠券取消选择成功",
                errorMessage = "优惠券取消选择错误",
                exceptionMessage = "优惠券取消选择异常",
                tokenIndex = tokenIndex
            )
        }

        return orderPlaceData
    }

    /**
     * 更新红包选择
     */
    private suspend fun updateRedPacketSelection(
        requestService: RequestService,
        selectedAddressItem: AddressItem,
        currentCartItem: CartItem,
        autoOrderPlaceData: AutoOrderPlaceData,
        orderPlaceData: OrderPlaceData,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): OrderPlaceData? {
        // 从价格信息组件中获取红包数据
        val priceInfo = WidgetHelper.getPriceInfoGroupData(orderPlaceData)
        val redPacketWidgetData =
            WidgetHelper.getSubWidgetDataFromGroup<RedPacketWidgetData>(
                priceInfo,
                "redpacket"
            ) ?: run {
                // 如果配置了红包但红包列表为空
                if (autoOrderPlaceData.selectedOrderPlaceRedPacket != null) {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                        tokenIndex,
                        "红包选择"
                    )
                    progressRecorder.recordProcess("可选择红包列表为空但已配置红包参数", "ERROR")
                    return null
                }
                progressRecorder.recordProcess("可选择红包列表为空但未配置红包参数", "WARNING")
                return orderPlaceData
            }

        // 获取当前优惠券数据，用于保持选择状态
        val couponsWidgetData =
            WidgetHelper.getSubWidgetDataFromGroup<CouponsWidgetData>(
                priceInfo,
                "coupons"
            )

        // 从红包数据中获取选中的红包代码
        val selectedRedPacketCode = redPacketWidgetData.selectedredpackets?.firstOrNull() ?: ""

        // 用户配置了红包
        if (autoOrderPlaceData.selectedOrderPlaceRedPacket != null) {
            val selectedOrderPlaceRedPacket = autoOrderPlaceData.selectedOrderPlaceRedPacket
            progressRecorder.recordProcess("选择红包: ${selectedOrderPlaceRedPacket.showDescription}")

            // 从红包数据中获取已选中的红包
            val selectedRedPacket =
                redPacketWidgetData.availableredpackets?.firstOrNull { it.code == selectedRedPacketCode }

            // 检查已选中的红包是否与用户配置的红包相同
            if (selectedRedPacket?.showDescription == selectedOrderPlaceRedPacket.showDescription &&
                selectedRedPacket.amount == selectedOrderPlaceRedPacket.amount
            ) {
                progressRecorder.recordProcess("已选择红包: ${selectedOrderPlaceRedPacket.showDescription}")
                return orderPlaceData
            }

            // 从红包数据中获取红包代码
            val redPacketCode = redPacketWidgetData.availableredpackets?.find {
                it.showDescription == selectedOrderPlaceRedPacket.showDescription &&
                        it.amount == selectedOrderPlaceRedPacket.amount
            }?.code ?: run {
                progressRecorder.recordProcess(
                    "未匹配到红包: ${selectedOrderPlaceRedPacket.showDescription}",
                    "WARNING"
                )
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                    tokenIndex,
                    "红包选择"
                )
                return null
            }

            return submitOrderPlaceUpdate(
                requestService,
                selectedAddressItem,
                currentCartItem,
                progressRecorder,
                selectedRedPacketCodeArr = "[\"${redPacketCode}\"]",
                selectedCouponCodeArr = if (couponsWidgetData?.selectedcoupons?.isNotEmpty() == true)
                    "[\"${couponsWidgetData.selectedcoupons[0]}\"]" else "",
                ispickself = orderPlaceData.ispickself.toString(),
                successMessage = "红包选择成功",
                errorMessage = "红包选择错误",
                exceptionMessage = "红包选择异常",
                tokenIndex = tokenIndex
            )
        } else if (selectedRedPacketCode.isNotEmpty()) {
            // 用户没有配置红包，但当前有选中的红包，需要取消选择
            progressRecorder.recordProcess("取消红包选择")

            return submitOrderPlaceUpdate(
                requestService,
                selectedAddressItem,
                currentCartItem,
                progressRecorder,
                selectedRedPacketCodeArr = "",
                selectedCouponCodeArr = if (couponsWidgetData?.selectedcoupons?.isNotEmpty() == true)
                    "[\"${couponsWidgetData.selectedcoupons[0]}\"]" else "",
                ispickself = orderPlaceData.ispickself.toString(),
                successMessage = "红包取消选择成功",
                errorMessage = "红包取消选择错误",
                exceptionMessage = "红包取消选择异常",
                tokenIndex = tokenIndex
            )
        }

        return orderPlaceData
    }

    /**
     * 更新积分支付选项
     */
    private suspend fun updatePointPayOption(
        requestService: RequestService,
        selectedAddressItem: AddressItem,
        currentCartItem: CartItem,
        autoOrderPlaceData: AutoOrderPlaceData,
        orderPlaceData: OrderPlaceData,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): OrderPlaceData? {
        progressRecorder.recordProcess("修改积分支付选项: ${autoOrderPlaceData.pointPayOption}")

        // 获取最新的价格信息组件数据
        val priceInfo = WidgetHelper.getPriceInfoGroupData(orderPlaceData)
        // 获取当前优惠券数据，用于保持选择状态
        val couponsWidgetData =
            WidgetHelper.getSubWidgetDataFromGroup<CouponsWidgetData>(
                priceInfo,
                "coupons"
            )
        // 获取当前红包数据，用于保持选择状态
        val redPacketWidgetData =
            WidgetHelper.getSubWidgetDataFromGroup<RedPacketWidgetData>(
                priceInfo,
                "redpacket"
            )

        return submitOrderPlaceUpdate(
            requestService,
            selectedAddressItem,
            currentCartItem,
            progressRecorder,
            pointpayoption = autoOrderPlaceData.pointPayOption.toString(),
            selectedCouponCodeArr = if (couponsWidgetData?.selectedcoupons?.isNotEmpty() == true)
                "[\"${couponsWidgetData.selectedcoupons[0]}\"]" else "",
            selectedRedPacketCodeArr = if (redPacketWidgetData?.selectedredpackets?.isNotEmpty() == true)
                "[\"${redPacketWidgetData.selectedredpackets[0]}\"]" else "",
            ispickself = orderPlaceData.ispickself.toString(),
            successMessage = "积分支付选项修改成功",
            errorMessage = "积分支付选项修改失败",
            exceptionMessage = "积分支付选项修改异常",
            tokenIndex = tokenIndex
        )
    }

    /**
     * 更新余额支付选项
     */
    private suspend fun updateBalancePayOption(
        requestService: RequestService,
        selectedAddressItem: AddressItem,
        currentCartItem: CartItem,
        autoOrderPlaceData: AutoOrderPlaceData,
        orderPlaceData: OrderPlaceData,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): OrderPlaceData? {
        progressRecorder.recordProcess("修改余额支付选项: ${autoOrderPlaceData.balancePayOption}")

        // 获取最新的价格信息组件数据
        val priceInfo = WidgetHelper.getPriceInfoGroupData(orderPlaceData)
        // 获取当前优惠券数据，用于保持选择状态
        val couponsWidgetData =
            WidgetHelper.getSubWidgetDataFromGroup<CouponsWidgetData>(
                priceInfo,
                "coupons"
            )
        // 获取当前红包数据，用于保持选择状态
        val redPacketWidgetData =
            WidgetHelper.getSubWidgetDataFromGroup<RedPacketWidgetData>(
                priceInfo,
                "redpacket"
            )

        return submitOrderPlaceUpdate(
            requestService,
            selectedAddressItem,
            currentCartItem,
            progressRecorder,
            pointpayoption = autoOrderPlaceData.pointPayOption.toString(),
            balancepayoption = autoOrderPlaceData.balancePayOption.toString(),
            selectedCouponCodeArr = if (couponsWidgetData?.selectedcoupons?.isNotEmpty() == true)
                "[\"${couponsWidgetData.selectedcoupons[0]}\"]" else "",
            selectedRedPacketCodeArr = if (redPacketWidgetData?.selectedredpackets?.isNotEmpty() == true)
                "[\"${redPacketWidgetData.selectedredpackets[0]}\"]" else "",
            ispickself = orderPlaceData.ispickself.toString(),
            successMessage = "余额支付选项修改成功",
            errorMessage = "余额支付选项修改错误",
            exceptionMessage = "余额支付选项修改异常",
            tokenIndex = tokenIndex
        )
    }

    /**
     * 验证支付总金额
     */
    private fun validateTotalPayment(
        autoOrderPlaceData: AutoOrderPlaceData,
        orderPlaceData: OrderPlaceData,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): Boolean {
        if (autoOrderPlaceData.totalPaymentNew.isEmpty()) {
            return true
        }

        if (orderPlaceData.totalpaymentNew != autoOrderPlaceData.totalPaymentNew) {
            progressRecorder.recordProcess(
                "实付金额不符: 期望${autoOrderPlaceData.totalPaymentNew}，实际${orderPlaceData.totalpaymentNew}",
                "WARNING"
            )
            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                tokenIndex,
                "实付金额校验"
            )
            return false
        }
        return true
    }

    /**
     * 确认订单
     */
    private suspend fun confirmOrder(
        requestService: RequestService,
        autoOrderPlaceData: AutoOrderPlaceData,
        orderPlaceData: OrderPlaceData,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int,
        genPayUrl: Boolean
    ): Boolean {
        val orderConfirmResult = withContext(Dispatchers.IO) {
            requestService.order.orderConfirm(
                orderPlaceData = orderPlaceData,
                comment = autoOrderPlaceData.orderComment,
                texpecttime = autoOrderPlaceData.expectTime
            )
        }

        when (orderConfirmResult) {
            is RequestResult.Success -> {
                val response = ResponseParserUtils.parseOrderConfirmResponse(
                    orderConfirmResult.data
                )
                if (response != null && response.code == 0) {
                    val payendtime = DateUtils.formatDate(
                        response.data.payendtime,
                        DateUtils.DatePattern.HH_MM_SS
                    )
                    progressRecorder.recordProcess("订单确认成功，支付结束时间: $payendtime")
                    if (genPayUrl && response.data.continuepay == 1) {
                        val orderId = response.data.orderid
                        handleOrderPrepayment(requestService, orderId, progressRecorder)
                    }
                    return true
                } else {
                    progressRecorder.recordProcess(
                        "订单确认错误: ${response?.message ?: "未知错误"}",
                        "ERROR"
                    )
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                        tokenIndex,
                        "提交订单"
                    )
                    return false
                }
            }

            is RequestResult.Error -> {
                progressRecorder.recordProcess(
                    "订单确认异常: ${orderConfirmResult.error.message}", "ERROR"
                )
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                    tokenIndex,
                    "提交订单"
                )
                return false
            }
        }
    }

    /**
     * 处理订单预支付
     *
     * @param requestService API服务
     * @param orderId 订单ID
     * @param progressRecorder 进度记录器
     */
    private suspend fun handleOrderPrepayment(
        requestService: RequestService,
        orderId: String,
        progressRecorder: ProcessRecorder
    ) {
        // 调用API进行订单预支付
        val result = withContext(Dispatchers.IO) {
            requestService.order.orderPrePay(
                orderId,
                "pay.dcep.app.pay.and.sign"
            )
        }
        when (result) {
            is RequestResult.Success -> {
                val response =
                    ResponseParserUtils.parseOrderPrepayResponse(result.data)
                if (response != null && response.code == 0) {
                    val payInfo = response.data?.payInfo
                    if (payInfo != null) {
                        progressRecorder.recordProcess("数字人名币支付链接,$orderId,$payInfo")
                    } else {
                        progressRecorder.recordProcess("数字人名币支付链接,$orderId,支付链接为空")
                    }
                } else {
                    val errorMsg = response?.message ?: "订单预支付错误"
                    progressRecorder.recordProcess(
                        "订单预支付错误: $orderId, $errorMsg",
                        "ERROR"
                    )
                }
            }
            is RequestResult.Error -> {
                progressRecorder.recordProcess(
                    "订单预支付异常: ${result.error.message}",
                    "ERROR"
                )
            }
        }
    }

    /**
     * 提交订单更新请求
     */
    private suspend fun submitOrderPlaceUpdate(
        requestService: RequestService,
        selectedAddressItem: AddressItem,
        currentCartItem: CartItem,
        progressRecorder: ProcessRecorder,
        selectedCouponCodeArr: String = "",
        selectedRedPacketCodeArr: String = "",
        ispickself: String = "",
        pointpayoption: String = "",
        balancepayoption: String = "",
        successMessage: String,
        errorMessage: String,
        exceptionMessage: String,
        tokenIndex: Int
    ): OrderPlaceData? {
        val resubmitResult = withContext(Dispatchers.IO) {
            requestService.order.orderPlace(
                selectedAddress = selectedAddressItem,
                selectedCart = currentCartItem,
                firstOrderPlace = false,
                selectedCouponCodeArr = selectedCouponCodeArr,
                selectedRedPacketCodeArr = selectedRedPacketCodeArr,
                ispickself = ispickself,
                pointpayoption = pointpayoption,
                balancepayoption = balancepayoption,
                onRefreshOrderPlaceParams = { url, orderPlaceBody ->
                    requestService.setOrderPlaceUrl(url)
                    requestService.setOrderPlaceBody(orderPlaceBody)
                }
            )
        }

        return when (resubmitResult) {
            is RequestResult.Success -> {
                val resubmitResponse =
                    ResponseParserUtils.parseOrderPlaceResponse(resubmitResult.data)
                if (resubmitResponse != null && resubmitResponse.code == 0) {
                    progressRecorder.recordProcess(successMessage)
                    resubmitResponse.data
                } else {
                    progressRecorder.recordProcess(
                        "$errorMessage: ${resubmitResponse?.message ?: "未知错误"}",
                        "ERROR"
                    )
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                        tokenIndex,
                        "提交订单"
                    )
                    null
                }
            }

            is RequestResult.Error -> {
                progressRecorder.recordProcess(
                    "$exceptionMessage: ${resubmitResult.error.message?.take(15)}", "ERROR"
                )
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                    tokenIndex,
                    "提交订单"
                )
                null
            }
        }
    }

    /**
     * 同步地址、店铺和XYH参数
     *
     * @param requestService API服务
     * @param autoOrderPlaceData 自动下单数据
     * @param progressRecorder 进度记录器
     */
    private suspend fun syncAddress(
        requestService: RequestService,
        autoOrderPlaceData: AutoOrderPlaceData,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): AddressItem? {
        progressRecorder.recordProcess("地址同步开始")

        val shopName = autoOrderPlaceData.addAddressShopItem?.shopname ?: ""

        try {
            // 获取地址列表
            val addressResult = withContext(Dispatchers.IO) {
                requestService.address.getAllAddress()
            }

            // 处理地址结果
            val selectedAddressItem = when (addressResult) {
                is RequestResult.Success -> {
                    val addressResponse =
                        ResponseParserUtils.parseAddressResponse(addressResult.data)
                    if (addressResponse != null && addressResponse.code == 0) {
                        if (!addressResponse.data?.list.isNullOrEmpty()) {
                            // 获取第一个地址
                            progressRecorder.recordProcess("地址同步成功")
                            addressResponse.data.list[0]
                        } else {
                            Log.e(TAG, "地址列表为空")
                            progressRecorder.recordProcess("地址列表为空", "WARNING")
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                tokenIndex,
                                "地址同步"
                            )
                            null
                        }
                    } else {
                        Log.e(TAG, "地址同步异常")
                        progressRecorder.recordProcess("地址同步异常", "ERROR")
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                            tokenIndex,
                            "地址同步"
                        )
                        null
                    }
                }

                is RequestResult.Error -> {
                    Log.e(TAG, "地址同步异常", addressResult.error)
                    progressRecorder.recordProcess(
                        "地址同步异常", "ERROR"
                    )
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                        tokenIndex,
                        "地址同步"
                    )
                    null
                }
            }

            // 如果有地址ID，继续获取店铺信息
            if (selectedAddressItem != null) {
                // 获取店铺信息
                val shopResult = withContext(Dispatchers.IO) {
                    requestService.shop.getFbShopLbs(selectedAddressItem)
                }

                // 处理店铺结果
                when (shopResult) {
                    is RequestResult.Success -> {
                        progressRecorder.recordProcess("店铺同步成功")

                        val shopResponse = ResponseParserUtils.parseShopResponse(shopResult.data)
                        if (shopResponse != null && shopResponse.code == 0) {
                            val addressInfo = shopResponse.data?.address?.firstOrNull()
                            val cityInfo = shopResponse.data?.city
                            val shopInfoList = shopResponse.data?.seller

                            // 如果店铺名不为空，则匹配店铺
                            var shopInfo: ShopInfo? = null
                            if (shopName.isNotEmpty()) {
                                shopInfo = shopInfoList?.find { it.shopname == shopName }
                                if (shopInfo == null) {
                                    progressRecorder.recordProcess("未匹配到店铺", "WARNING")
                                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                        tokenIndex,
                                        "店铺同步"
                                    )
                                    return null
                                }
                            } else {
                                shopInfo = shopInfoList?.firstOrNull()
                                if (shopInfo == null) {
                                    progressRecorder.recordProcess("店铺列表为空", "WARNING")
                                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                        tokenIndex,
                                        "店铺同步"
                                    )
                                    return null
                                }
                            }

                            if (addressInfo != null && cityInfo != null) {
                                val sellerId = shopInfo.sellerid.toString()
                                val shopId = shopInfo.shopid
                                val addressId = addressInfo.id
                                val lat = addressInfo.location?.lat ?: ""
                                val lng = addressInfo.location?.lng ?: ""
                                val cityId = cityInfo.id
                                val district = addressInfo.address.district

                                // 设置店铺ID和销售商ID
                                requestService.setShopId(shopId)
                                requestService.setSellerId(sellerId)

                                // 设置城市ID和区域
                                requestService.setCityId(cityId)
                                requestService.setDistrict(district)

                                val xyhResult = requestService.getXyhBizParams(
                                    lat = lat,
                                    lng = lng,
                                    cityid = cityId,
                                    district = district,
                                    sellerid = sellerId,
                                    shopid = shopId,
                                    addressId = addressId,
                                    serviceType = requestService.serviceType
                                )

                                when (xyhResult) {
                                    is RequestResult.Success -> {
                                        val xyhBizParams = xyhResult.data
                                        requestService.setXyhBizParams(xyhBizParams)
                                        val webXyhBizParams =
                                            AddressUtils.matchAndAssignParamsByXYHBizParamsCommon(
                                                xyhBizParams
                                            )
                                        requestService.setWebXyhBizParams(webXyhBizParams)
                                        progressRecorder.recordProcess("XYH参数同步成功")
                                        return selectedAddressItem
                                    }

                                    is RequestResult.Error -> {
                                        Log.e(TAG, "XYH参数同步错误", xyhResult.error)
                                        progressRecorder.recordProcess(
                                            "XYH参数同步错误", "ERROR"
                                        )
                                        return null
                                    }
                                }
                            } else {
                                progressRecorder.recordProcess("店铺信息为空", "WARNING")
                                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                    tokenIndex,
                                    "店铺同步"
                                )
                                return null
                            }
                        } else {
                            progressRecorder.recordProcess("店铺列表为空", "WARNING")
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                tokenIndex,
                                "店铺同步"
                            )
                            return null
                        }
                    }

                    is RequestResult.Error -> {
                        Log.e(TAG, "同步店铺错误", shopResult.error)
                        progressRecorder.recordProcess(
                            "店铺同步异常", "ERROR"
                        )
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                            tokenIndex,
                            "店铺同步"
                        )
                        return null
                    }
                }
            } else {
                progressRecorder.recordProcess("地址信息为空", "WARNING")
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                    tokenIndex,
                    "地址同步"
                )
                return null
            }

            // 如果执行到这里，说明有问题，返回null
            return null
        } catch (e: Exception) {
            Log.e(TAG, "Error syncing address", e)
            progressRecorder.recordProcess("地址同步异常", "ERROR")
            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                tokenIndex,
                "地址同步"
            )
            return null
        }
    }

    /**
     * 同步购物车信息
     *
     * @param requestService API服务
     * @param autoOrderPlaceData 自动下单数据
     * @param progressRecorder 进度记录器
     * @param selectedAddressItem 选中的地址项
     * @return 当前购物车项，如果同步失败则返回null
     */
    private suspend fun syncCart(
        requestService: RequestService,
        autoOrderPlaceData: AutoOrderPlaceData,
        progressRecorder: ProcessRecorder,
        selectedAddressItem: AddressItem,
        tokenIndex: Int
    ): CartItem? {
        progressRecorder.recordProcess("购物车同步开始")

        val productConfigStr = autoOrderPlaceData.productConfigStr

        if (productConfigStr.isEmpty()) {
            Log.e(TAG, "商品配置为空，不进行购物车匹配")
            progressRecorder.recordProcess("商品配置为空，不进行购物车匹配", "WARNING")
        }

        try {
            // 调用API获取购物车数据
            val cartResult = withContext(Dispatchers.IO) {
                requestService.cart.getAllCart(selectedAddressItem.id)
            }

            // 处理购物车结果
            when (cartResult) {
                is RequestResult.Success -> {
                    val cartResponse = ResponseParserUtils.parseCartResponse(cartResult.data)
                    if (cartResponse != null && cartResponse.code == 0) {
                        val cartItems = cartResponse.data?.cartlist
                        if (!cartItems.isNullOrEmpty()) {
                            if (productConfigStr.isEmpty()) {
                                progressRecorder.recordProcess("购物车同步成功")
                                return cartItems.firstOrNull()
                            }
                            // 构建匹配商品字符串
                            val models = mutableListOf<CartModelWrapper>()
                            cartItems.forEach { cartItem ->
                                cartItem.cartModels.forEach { model ->
                                    when (model.modelType) {
                                        CartModelTypes.PRODUCT_ITEM -> {
                                            models.add(
                                                CartModelWrapper(
                                                    data = model.data,
                                                    modelType = model.modelType
                                                )
                                            )
                                        }
                                    }
                                    models
                                }
                                val productConfigs = collectProductConfig(models)
                                if (compareProductConfigs(productConfigs, productConfigStr)) {
                                    progressRecorder.recordProcess("购物车同步并匹配成功")
                                    return cartItem
                                }
                            }
                            progressRecorder.recordProcess("购物车同步成功但匹配未成功")
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                tokenIndex,
                                "购物车同步"
                            )
                            return null
                        } else {
                            progressRecorder.recordProcess("购物车列表为空", "WARNING")
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                                tokenIndex,
                                "购物车同步"
                            )
                            return null
                        }
                    } else {
                        val errorMsg = cartResponse?.message ?: "购物车获取错误"
                        Log.e(TAG, "购物车同步错误: $errorMsg")
                        progressRecorder.recordProcess("购物车同步错误", "ERROR")
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                            tokenIndex,
                            "购物车同步"
                        )
                        return null
                    }
                }

                is RequestResult.Error -> {
                    Log.e(TAG, "购物车同步异常", cartResult.error)
                    progressRecorder.recordProcess("购物车同步异常", "ERROR")
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                        tokenIndex,
                        "购物车同步"
                    )
                    return null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "购物车同步异常", e)
            progressRecorder.recordProcess("购物车同步异常", "ERROR")
            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
                tokenIndex,
                "购物车同步"
            )
            return null
        }
    }
}

/**
 * 比较两个商品配置字符串是否匹配
 * 解析每个配置项并比较参数，只要所有参数匹配即可
 *
 * @param config1 第一个商品配置字符串
 * @param config2 第二个商品配置字符串
 * @return 是否匹配
 */
fun compareProductConfigs(config1: String, config2: String): Boolean {
    // 如果两个字符串完全相同，直接返回true
    if (config1 == config2) return true

    // 如果其中一个为空而另一个不为空，返回false
    if (config1.isEmpty() || config2.isEmpty()) return false

    // 解析两个配置字符串为商品参数列表
    val products1 = parseProductConfig(config1)
    val products2 = parseProductConfig(config2)

    // 如果商品数量不同，返回false
    if (products1.size != products2.size) return false

    // 对两个列表进行排序，确保顺序一致
    val sortedProducts1 = products1.sortedWith(compareBy({ it["id"] }, { it["num"] }))
    val sortedProducts2 = products2.sortedWith(compareBy({ it["id"] }, { it["num"] }))

    // 逐个比较商品参数
    return sortedProducts1.zip(sortedProducts2).all { (p1, p2) ->
        p1["id"] == p2["id"] &&
                p1["num"] == p2["num"] &&
                p1["goodstagid"] == p2["goodstagid"] &&
                p1["bundlepromocode"] == p2["bundlepromocode"]
    }
}

/**
 * 解析商品配置字符串为参数映射列表
 *
 * @param config 商品配置字符串
 * @return 参数映射列表
 */
private fun parseProductConfig(config: String): List<Map<String, String>> {
    return config.trim(';').split(";").map { item ->
        val parts = item.split(",")
        val params = mutableMapOf<String, String>()

        // 解析必须参数：id和num
        if (parts.size >= 2) {
            params["id"] = parts[0]
            params["num"] = parts[1]
        }

        // 解析可选参数：goodstagid
        if (parts.size >= 3) {
            params["goodstagid"] = parts[2]
        } else {
            params["goodstagid"] = "0"
        }

        // 解析可选参数：bundlepromocode
        if (parts.size >= 4) {
            params["bundlepromocode"] = parts[3]
        } else {
            params["bundlepromocode"] = ""
        }

        params
    }
}

/**
 * 检查自动下单状态，如果已关闭则记录错误并返回true表示应该停止执行
 *
 * @param viewModel ViewModel实例
 * @param progressRecorder 进度记录器
 * @param tokenIndex 令牌索引
 * @param operationName 操作名称
 * @return true表示应该停止执行，false表示可以继续
 */
private fun checkAutoOrderPlaceStatus(
    viewModel: OrderViewModel,
    progressRecorder: ProcessRecorder,
    tokenIndex: Int,
    operationName: String
): Boolean {
    // 获取当前自动下单状态并保存到局部变量，避免在多线程环境中被意外修改
    val isAutoOrderPlaceActive = viewModel.autoOrderPlace.value

    if (!isAutoOrderPlaceActive) {
        Log.d(
            "AutoOrderPlaceUtils",
            "Auto order place is disabled before $operationName, cancelling task"
        )
        progressRecorder.recordProcess("自动下单已关闭，停止执行操作", "ERROR")
        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
            FailedTokenIndexRecordUtils.OperationType.ORDER_OPERATION,
            tokenIndex,
            operationName
        )
        return true
    }
    return false
}

