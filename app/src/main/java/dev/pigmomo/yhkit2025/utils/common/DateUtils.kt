package dev.pigmomo.yhkit2025.utils.common

import android.util.Log
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 日期工具类
 * 提供日期格式化和处理的通用方法
 */
object DateUtils {

    private const val TAG = "DateUtils"

    /**
     * 日期格式常量
     */
    object DatePattern {
        const val FULL_DATE_TIME = "yyyy-MM-dd HH:mm:ss"
        const val DATE_TIME = "yyyy-MM-dd HH:mm"
        const val DATE_ONLY = "yyyy-MM-dd"
        const val DATE_MONTH_DAY = "MM-dd"
        const val DATE_WITH_WEEKDAY = "MM月dd日 EE"
        const val DATE_DOT_FORMAT = "MM.dd.yy"
        const val HH_MM_SS = "HH:mm:ss"
    }

    /**
     * 将时间戳格式化为"今天"、"明天"或具体的年月日格式，并显示星期几
     *
     * @param timestamp 时间戳（毫秒）
     * @param showYearForOtherDays 是否在其他日期显示年份，默认为true
     * @return 格式化后的日期字符串
     */
    fun formatDateRelative(timestamp: Long, showYearForOtherDays: Boolean = true): String {
        val targetDate = Calendar.getInstance()
        targetDate.timeInMillis = timestamp

        val today = Calendar.getInstance()

        // 重置时分秒，只比较日期部分
        resetTimeToMidnight(targetDate)
        resetTimeToMidnight(today)

        // 计算日期差
        val diffDays = (targetDate.timeInMillis - today.timeInMillis) / (24 * 60 * 60 * 1000)

        // 获取星期几
        val weekday = formatDate(timestamp, "EE")

        return when (diffDays) {
            0L -> "今天($weekday)"
            1L -> "明天($weekday)"
            else -> {
                // 其他日期显示具体的年月日
                val pattern = if (showYearForOtherDays) DatePattern.DATE_ONLY else DatePattern.DATE_MONTH_DAY
                formatDate(timestamp, pattern)
            }
        }
    }

    /**
     * 将时间戳格式化为带星期的日期格式
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的日期字符串，如"MM月dd日 星期几"
     */
    fun formatDateWithWeekday(timestamp: Long): String {
        return formatDate(timestamp, DatePattern.DATE_WITH_WEEKDAY)
    }

    /**
     * 格式化时间戳为指定格式的日期字符串
     *
     * @param timestamp 时间戳（毫秒）
     * @param pattern 日期格式模式
     * @return 格式化后的日期字符串，格式化失败返回"未知"
     */
    fun formatDate(timestamp: Long, pattern: String = DatePattern.DATE_TIME): String {
        return try {
            SimpleDateFormat(pattern, Locale.getDefault()).format(Date(timestamp))
        } catch (e: Exception) {
            Log.e(TAG, "Error formatting date: $timestamp with pattern: $pattern", e)
            "未知"
        }
    }

    /**
     * 将时间戳格式化为完整的日期时间格式
     *
     * @param timestamp 时间戳（毫秒）
     * @param pattern 日期格式模式，默认为"yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的日期时间字符串
     */
    fun formatDateTime(timestamp: Long, pattern: String = DatePattern.FULL_DATE_TIME): String {
        return formatDate(timestamp, pattern)
    }

    /**
     * 将时间戳格式化为日期时间字符串
     * 与formatDateTime功能相同，提供兼容性
     *
     * @param timestamp 时间戳（毫秒）
     * @param pattern 日期格式模式，默认为"yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的日期时间字符串
     */
    fun formatTimestamp(timestamp: Long, pattern: String = DatePattern.FULL_DATE_TIME): String {
        return formatDate(timestamp, pattern)
    }

    /**
     * 获取当前日期的特定格式字符串
     *
     * @param pattern 日期格式模式
     * @return 格式化后的当前日期字符串
     */
    fun getCurrentDateString(pattern: String = DatePattern.DATE_DOT_FORMAT): String {
        return formatDate(System.currentTimeMillis(), pattern)
    }

    /**
     * 解析日期字符串为Date对象
     *
     * @param dateString 日期字符串
     * @param pattern 日期格式模式
     * @return 解析后的Date对象，解析失败返回null
     */
    fun parseDate(dateString: String, pattern: String = DatePattern.FULL_DATE_TIME): Date? {
        return try {
            SimpleDateFormat(pattern, Locale.getDefault()).parse(dateString)
        } catch (e: ParseException) {
            Log.e(TAG, "Error parsing date: $dateString with pattern: $pattern", e)
            null
        }
    }

    /**
     * 将日期时间字符串解析为时间戳
     *
     * @param dateTimeStr 日期时间字符串
     * @param pattern 日期格式模式，默认为 "yyyy-MM-dd HH:mm:ss"
     * @return 时间戳，解析失败则返回0
     */
    fun parseDateTime(dateTimeStr: String, pattern: String = DatePattern.FULL_DATE_TIME): Long {
        return try {
            parseDate(dateTimeStr, pattern)?.time ?: 0
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing date time: $dateTimeStr", e)
            0
        }
    }

    /**
     * 重置日历对象的时分秒为零点
     *
     * @param calendar 要重置的日历对象
     */
    private fun resetTimeToMidnight(calendar: Calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
    }
}