package dev.pigmomo.yhkit2025.utils.common

import android.annotation.SuppressLint
import android.content.Context
import android.provider.Settings
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONObject
import java.io.IOException

/**
 * 设备信息工具类
 */
object DeviceInfoUtils {

    /**
     * 获取公网IP地址
     */
    private suspend fun getIp(): String? {
        return withContext(Dispatchers.IO) {
            val client = OkHttpClient()
            val request = Request.Builder()
                .url("https://ipinfo.io/json")
                .build()

            try {
                client.newCall(request).execute().use { response ->
                    if (response.isSuccessful) {
                        response.body?.string()?.let { result ->
                            JSONObject(result).optString("ip").takeIf { it.isNotEmpty() }
                        }
                    } else {
                        Log.e("DeviceInfoUtils", "getIp error: status code ${response.code}")
                        null
                    }
                }
            } catch (ex: IOException) {
                Log.e("DeviceInfoUtils", "getIp error: ${ex.message}")
                null
            }
        }
    }

    /**
     * 获取设备唯一ID
     */
    @SuppressLint("HardwareIds")
    fun getUniqueID(context: Context): String {
        return Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
    }

    /**
     * 获取设备信息（IP和设备ID）
     */
    suspend fun getDeviceInfo(context: Context): String {
        val ip = getIp() ?: "IP"
        val deviceId = getUniqueID(context)
        return "IP,ID: $ip,$deviceId"
    }
}