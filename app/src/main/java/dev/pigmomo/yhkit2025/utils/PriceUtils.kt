package dev.pigmomo.yhkit2025.utils

import android.annotation.SuppressLint
import java.text.DecimalFormat

/**
 * 价格工具类
 * 用于处理价格格式化相关操作
 */
object PriceUtils {
    
    /**
     * 格式化价格（分转元）
     * @param priceInCents 价格（分）
     * @return 格式化后的价格字符串（元）
     */
    @SuppressLint("DefaultLocale")
    fun formatPrice(priceInCents: Int): String {
        val df = DecimalFormat("0.00")
        return df.format(priceInCents / 100.0)
    }
    
    /**
     * 格式化价格（分转元）- 带货币符号
     * @param priceInCents 价格（分）
     * @return 格式化后的价格字符串（元），带¥符号
     */
    @SuppressLint("DefaultLocale")
    fun formatPriceWithSymbol(priceInCents: Int): String {
        return "¥${formatPrice(priceInCents)}"
    }
    
    /**
     * 格式化数量
     * @param num 数量（可能是百分制）
     * @return 格式化后的数量字符串
     */
    fun formatNum(num: Int): String {
        val df = DecimalFormat("0")
        return df.format(num / 100)
    }
}
