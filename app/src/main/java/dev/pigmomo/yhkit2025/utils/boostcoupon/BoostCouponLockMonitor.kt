package dev.pigmomo.yhkit2025.utils.boostcoupon

import android.util.Log
import dev.pigmomo.yhkit2025.utils.boostcoupon.GameCodeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 助力券锁定监控工具类
 *
 * 提供锁定状态的实时监控、统计和调试功能
 */
object BoostCouponLockMonitor {
    private const val TAG = "BoostCouponLockMonitor"

    // 监控任务状态
    private val isMonitoring = AtomicBoolean(false)
    private var monitoringJob: Job? = null

    /**
     * 开始监控锁定状态
     *
     * @param intervalMs 监控间隔（毫秒），默认10秒
     * @param enableDetailedLog 是否启用详细日志，默认false
     */
    fun startMonitoring(intervalMs: Long = 10_000L, enableDetailedLog: Boolean = false) {
        if (isMonitoring.compareAndSet(false, true)) {
            monitoringJob = CoroutineScope(Dispatchers.IO).launch {
                Log.i(TAG, "Lock monitoring started with interval: ${intervalMs}ms")

                while (isActive && isMonitoring.get()) {
                    try {
                        val (total, soonExpired) = GameCodeManager.getLockStatistics()

                        if (total > 0) {
                            Log.i(TAG, "Lock Status - Total: $total, Soon Expired: $soonExpired")

                            if (enableDetailedLog) {
                                val lockInfoList = GameCodeManager.getLockStatusInfo()
                                lockInfoList.forEach { lockInfo ->
                                    Log.d(TAG, "Lock Detail - Key: ${lockInfo.key}, " +
                                            "Remaining: ${lockInfo.remainingTime}ms")
                                }
                            }

                            // 如果有即将过期的锁定，发出警告
                            if (soonExpired > 0) {
                                Log.w(TAG, "Warning: $soonExpired locks will expire soon!")
                            }
                        }

                        delay(intervalMs)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error during monitoring", e)
                        delay(intervalMs)
                    }
                }

                Log.i(TAG, "Lock monitoring stopped")
            }
        } else {
            Log.w(TAG, "Monitoring is already running")
        }
    }

    /**
     * 停止监控
     */
    fun stopMonitoring() {
        if (isMonitoring.compareAndSet(true, false)) {
            monitoringJob?.cancel()
            monitoringJob = null
            Log.i(TAG, "Lock monitoring stopped by user")
        }
    }

    /**
     * 获取当前锁定状态报告
     *
     * @return 格式化的状态报告字符串
     */
    fun getLockStatusReport(): String {
        val (total, soonExpired) = GameCodeManager.getLockStatistics()
        val lockInfoList = GameCodeManager.getLockStatusInfo()

        val report = StringBuilder()
        report.appendLine("=== 助力券锁定状态报告 ===")
        report.appendLine("总锁定数: $total")
        report.appendLine("即将过期数: $soonExpired")
        report.appendLine()

        if (lockInfoList.isNotEmpty()) {
            report.appendLine("详细锁定信息:")
            lockInfoList.forEachIndexed { index, lockInfo ->
                val remainingSeconds = lockInfo.remainingTime / 1000
                report.appendLine("${index + 1}. ${lockInfo.key} (剩余: ${remainingSeconds}秒)")
            }
        } else {
            report.appendLine("当前无锁定状态")
        }

        report.appendLine("========================")
        return report.toString()
    }

    /**
     * 执行锁定状态健康检查
     *
     * @return 检查结果和建议
     */
    fun performHealthCheck(): HealthCheckResult {
        val (total, soonExpired) = GameCodeManager.getLockStatistics()
        val lockInfoList = GameCodeManager.getLockStatusInfo()

        val issues = mutableListOf<String>()
        val suggestions = mutableListOf<String>()

        // 检查是否有过多的锁定
        if (total > 50) {
            issues.add("锁定数量过多: $total")
            suggestions.add("考虑调用 forceCleanupAllLocks() 清理所有锁定")
        }

        // 检查是否有即将过期的锁定
        if (soonExpired > 0) {
            issues.add("有 $soonExpired 个锁定即将过期")
            suggestions.add("这些锁定将在3秒内自动释放")
        }

        // 检查是否有长时间占用的锁定
        val longRunningLocks = lockInfoList.filter { it.remainingTime < 3000 }
        if (longRunningLocks.isNotEmpty()) {
            issues.add("有 ${longRunningLocks.size} 个锁定已运行超过12秒")
            suggestions.add("检查相关的网络请求是否存在问题")
        }

        val status = when {
            issues.isEmpty() -> HealthStatus.HEALTHY
            soonExpired > 0 || longRunningLocks.isNotEmpty() -> HealthStatus.WARNING
            total > 50 -> HealthStatus.CRITICAL
            else -> HealthStatus.WARNING
        }

        return HealthCheckResult(status, issues, suggestions, total, soonExpired)
    }

    /**
     * 健康检查状态枚举
     */
    enum class HealthStatus {
        HEALTHY,    // 健康
        WARNING,    // 警告
        CRITICAL    // 严重
    }

    /**
     * 健康检查结果数据类
     */
    data class HealthCheckResult(
        val status: HealthStatus,
        val issues: List<String>,
        val suggestions: List<String>,
        val totalLocks: Int,
        val soonExpiredLocks: Int
    ) {
        fun getFormattedReport(): String {
            val report = StringBuilder()
            report.appendLine("=== 锁定健康检查报告 ===")
            report.appendLine("状态: $status")
            report.appendLine("总锁定数: $totalLocks")
            report.appendLine("即将过期数: $soonExpiredLocks")
            report.appendLine()

            if (issues.isNotEmpty()) {
                report.appendLine("发现的问题:")
                issues.forEachIndexed { index, issue ->
                    report.appendLine("${index + 1}. $issue")
                }
                report.appendLine()
            }

            if (suggestions.isNotEmpty()) {
                report.appendLine("建议:")
                suggestions.forEachIndexed { index, suggestion ->
                    report.appendLine("${index + 1}. $suggestion")
                }
            }

            report.appendLine("========================")
            return report.toString()
        }
    }

    /**
     * 是否正在监控
     */
    fun isMonitoring(): Boolean = isMonitoring.get()
}